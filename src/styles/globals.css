@tailwind base;
@tailwind components;
@tailwind utilities;

/* Modern 2024 base styles with dynamic background */
html, body {
  background-color: #0f172a;
  color: #f1f5f9;
  font-feature-settings: "rlig" 1, "calt" 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  /* Prevent horizontal scrolling */
  overflow-x: hidden;
  max-width: 100vw;
  width: 100%;
}

body {
  /* Modern dynamic background with enhanced depth */
  background-image:
    radial-gradient(circle at 20% 80%, rgba(37, 99, 235, 0.12) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(139, 92, 246, 0.12) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(16, 185, 129, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 60% 60%, rgba(236, 72, 153, 0.06) 0%, transparent 50%),
    radial-gradient(circle at 90% 10%, rgba(245, 158, 11, 0.04) 0%, transparent 50%);
  background-attachment: fixed;
  background-size: 100% 100%, 80% 80%, 120% 120%, 90% 90%, 110% 110%;
}

/* Modern glass morphism utility classes */
.glass {
  border: 1px solid rgba(148, 163, 184, 0.2);
  background: rgba(15, 23, 42, 0.8);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
}

.glass-card {
  border: 1px solid rgba(148, 163, 184, 0.1);
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.95) 0%, rgba(30, 41, 59, 0.9) 50%, rgba(15, 23, 42, 0.95) 100%);
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
}

/* Graph container with consistent background */
.graph-container {
  width: 100%;
  height: 100vh;
  position: relative;
  /* Match the body background exactly */
  background-color: #0f172a;
  background-image:
    radial-gradient(circle at 20% 80%, rgba(37, 99, 235, 0.12) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(139, 92, 246, 0.12) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(16, 185, 129, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 60% 60%, rgba(236, 72, 153, 0.06) 0%, transparent 50%),
    radial-gradient(circle at 90% 10%, rgba(245, 158, 11, 0.04) 0%, transparent 50%);
  background-attachment: fixed;
  background-size: 100% 100%, 80% 80%, 120% 120%, 90% 90%, 110% 110%;
}

/* Canvas specific styling to ensure transparency */
canvas {
  background: transparent !important;
  background-color: transparent !important;
  background-image: none !important;
}

/* Enhanced controls */
.controls {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 10;
  padding: 1rem;
  border: 1px solid rgba(148, 163, 184, 0.1);
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.95) 0%, rgba(30, 41, 59, 0.9) 50%, rgba(15, 23, 42, 0.95) 100%);
  backdrop-filter: blur(16px);
  border-radius: 0.75rem;
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
}

/* Modern search bar */
.search-bar {
  width: 100%;
  max-width: 32rem;
  padding: 0.75rem;
  border: 1px solid rgba(148, 163, 184, 0.1);
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.95) 0%, rgba(30, 41, 59, 0.9) 50%, rgba(15, 23, 42, 0.95) 100%);
  backdrop-filter: blur(16px);
  border-radius: 0.75rem;
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
}

/* Node styles */
.node {
  cursor: pointer;
  transition: all 0.2s ease-in-out;
}

.node:hover {
  filter: brightness(1.2) drop-shadow(0 0 10px rgba(139, 92, 246, 0.4));
}

/* Enhanced tooltip with highest z-index and compact styling */
.node-tooltip {
  padding: 0.75rem;
  font-size: 0.875rem;
  border-radius: 0.5rem;
  border: 1px solid rgba(148, 163, 184, 0.1);
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.95) 0%, rgba(30, 41, 59, 0.9) 50%, rgba(15, 23, 42, 0.95) 100%);
  backdrop-filter: blur(16px);
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
  z-index: 99999 !important;
}

/* Compact tooltip styling for ResponsiveContainer elements */
.tooltip-compact {
  padding: 0.375rem 0.5rem !important;
  font-size: 0.75rem !important;
  line-height: 1.4 !important;
  max-width: 320px !important;
  border-radius: 0.5rem !important;
  z-index: 99999 !important;
  position: fixed !important;
  pointer-events: none !important;
  backdrop-filter: blur(20px) !important;
  -webkit-backdrop-filter: blur(20px) !important;
  background: rgba(17, 24, 39, 0.95) !important;
  border: 1px solid rgba(156, 163, 175, 0.3) !important;
  box-shadow:
    0 10px 25px rgba(0, 0, 0, 0.4),
    0 4px 12px rgba(139, 92, 246, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
  transform: none !important; /* Override any transform to prevent positioning issues */
  /* Allow text wrapping and scrolling for long content */
  white-space: normal !important;
  word-wrap: break-word !important;
  overflow-wrap: break-word !important;
  max-height: min(300px, 70vh) !important;
  overflow-y: auto !important;
  overflow-x: hidden !important;
  /* Enhanced scrolling behavior */
  scroll-behavior: smooth !important;
  scrollbar-width: thin !important;
  scrollbar-color: rgba(139, 92, 246, 0.5) rgba(15, 20, 25, 0.6) !important;
}

/* Scrollbar styling for compact tooltips */
.tooltip-compact::-webkit-scrollbar {
  width: 4px !important;
  height: 4px !important;
}

.tooltip-compact::-webkit-scrollbar-track {
  background: rgba(15, 20, 25, 0.6) !important;
  border-radius: 9999px !important;
  margin: 2px 0 !important;
}

.tooltip-compact::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.6) 0%, rgba(168, 85, 247, 0.5) 100%) !important;
  border-radius: 9999px !important;
  border: 1px solid rgba(139, 92, 246, 0.2) !important;
  transition: all 0.2s ease-in-out !important;
  min-height: 20px !important;
}

.tooltip-compact::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.8) 0%, rgba(168, 85, 247, 0.7) 100%) !important;
  border-color: rgba(139, 92, 246, 0.4) !important;
  box-shadow: 0 0 6px rgba(139, 92, 246, 0.3) !important;
}

/* Risk Filter Slider Styling */
.slider {
  -webkit-appearance: none;
  appearance: none;
  background: transparent;
  cursor: pointer;
}

.slider::-webkit-slider-track {
  background: rgba(30, 41, 59, 0.8);
  height: 8px;
  border-radius: 4px;
  border: 1px solid rgba(148, 163, 184, 0.2);
}

.slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  height: 20px;
  width: 20px;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(251, 191, 36, 0.9) 0%, rgba(245, 158, 11, 0.8) 100%);
  border: 2px solid rgba(251, 191, 36, 0.3);
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(251, 191, 36, 0.3);
  transition: all 0.2s ease-in-out;
}

.slider::-webkit-slider-thumb:hover {
  background: linear-gradient(135deg, rgba(251, 191, 36, 1) 0%, rgba(245, 158, 11, 0.9) 100%);
  border-color: rgba(251, 191, 36, 0.5);
  box-shadow: 0 4px 12px rgba(251, 191, 36, 0.4);
  transform: scale(1.1);
}

.slider::-moz-range-track {
  background: rgba(30, 41, 59, 0.8);
  height: 8px;
  border-radius: 4px;
  border: 1px solid rgba(148, 163, 184, 0.2);
}

.slider::-moz-range-thumb {
  height: 20px;
  width: 20px;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(251, 191, 36, 0.9) 0%, rgba(245, 158, 11, 0.8) 100%);
  border: 2px solid rgba(251, 191, 36, 0.3);
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(251, 191, 36, 0.3);
  transition: all 0.2s ease-in-out;
}

/* Ensure tooltips are always on top */
[role="tooltip"] {
  z-index: 99999 !important;
  position: fixed !important;
}

/* Enhanced bubble styling with sophisticated effects */
.bubble-node {
  transition: all 0.5s ease-out;
  filter: drop-shadow(0 3px 12px rgba(0, 0, 0, 0.4));
  animation: subtle-float 6s ease-in-out infinite;
}

.bubble-node:hover {
  filter: drop-shadow(0 6px 24px rgba(139, 92, 246, 0.6)) brightness(1.3);
  transform: scale(1.08) translateY(-2px);
  animation-play-state: paused;
}

.bubble-node.selected {
  filter: drop-shadow(0 8px 32px rgba(139, 92, 246, 0.8)) brightness(1.4);
  animation: pulse-glow-enhanced 2s ease-in-out infinite;
}

.bubble-link {
  transition: all 0.5s ease-out;
  opacity: 0.5;
  filter: drop-shadow(0 1px 4px rgba(0, 0, 0, 0.3));
}

.bubble-link:hover {
  opacity: 0.85;
  filter: drop-shadow(0 2px 8px rgba(139, 92, 246, 0.4));
}

.bubble-link.highlighted {
  opacity: 0.95;
  filter: drop-shadow(0 3px 12px rgba(139, 92, 246, 0.5));
  animation: connection-flow 3s linear infinite;
}

/* New floating animation */
@keyframes subtle-float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  25% { transform: translateY(-2px) rotate(0.5deg); }
  50% { transform: translateY(-1px) rotate(0deg); }
  75% { transform: translateY(-3px) rotate(-0.5deg); }
}

/* Enhanced connection flow animation */
@keyframes connection-flow {
  0% { stroke-dashoffset: 0; }
  100% { stroke-dashoffset: 20; }
}

/* Slide up animation for FAB menu */
@keyframes slide-up {
  0% {
    opacity: 0;
    transform: translateY(20px) scale(0.9);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.animate-slide-up {
  animation: slide-up 0.3s ease-out;
}

/* Enhanced dropdown animations */
@keyframes dropdownSlide {
  0% {
    opacity: 0;
    transform: translateY(-10px) scale(0.95);
    filter: blur(4px);
  }
  50% {
    opacity: 0.8;
    transform: translateY(-2px) scale(0.98);
    filter: blur(1px);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
    filter: blur(0);
  }
}

@keyframes dropdownFadeIn {
  0% {
    opacity: 0;
    transform: translateY(-8px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes optionSlideIn {
  0% {
    opacity: 0;
    transform: translateX(-10px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Dropdown utility classes */
.dropdown-enter {
  animation: dropdownSlide 0.3s cubic-bezier(0.16, 1, 0.3, 1) forwards;
}

.dropdown-option-enter {
  animation: optionSlideIn 0.2s ease-out forwards;
}

/* Modern shadow and glow effects */
.shadow-glow {
  box-shadow:
    0 0 20px rgba(139, 92, 246, 0.4),
    0 4px 20px rgba(0, 0, 0, 0.3);
}

.shadow-glow-blue {
  box-shadow:
    0 0 20px rgba(37, 99, 235, 0.4),
    0 4px 20px rgba(0, 0, 0, 0.3);
}

.shadow-glow-green {
  box-shadow:
    0 0 20px rgba(16, 185, 129, 0.4),
    0 4px 20px rgba(0, 0, 0, 0.3);
}

.shadow-glow-pink {
  box-shadow:
    0 0 20px rgba(236, 72, 153, 0.4),
    0 4px 20px rgba(0, 0, 0, 0.3);
}

/* Enhanced cursor styles */
.cursor-grab {
  cursor: grab;
}

.cursor-grabbing {
  cursor: grabbing;
}

/* Enhanced animations with bubble maps inspired effects */
@keyframes pulse-glow-enhanced {
  0% {
    transform: scale(1) rotate(0deg);
    opacity: 1;
    filter: drop-shadow(0 0 8px rgba(139, 92, 246, 0.4)) brightness(1.2);
  }
  25% {
    transform: scale(1.05) rotate(0.5deg);
    opacity: 0.95;
    filter: drop-shadow(0 0 16px rgba(139, 92, 246, 0.6)) brightness(1.3);
  }
  50% {
    transform: scale(1.1) rotate(0deg);
    opacity: 0.9;
    filter: drop-shadow(0 0 24px rgba(139, 92, 246, 0.8)) brightness(1.4);
  }
  75% {
    transform: scale(1.05) rotate(-0.5deg);
    opacity: 0.95;
    filter: drop-shadow(0 0 16px rgba(139, 92, 246, 0.6)) brightness(1.3);
  }
  100% {
    transform: scale(1) rotate(0deg);
    opacity: 1;
    filter: drop-shadow(0 0 8px rgba(139, 92, 246, 0.4)) brightness(1.2);
  }
}

.node-pulse {
  animation: pulse-glow-enhanced 3s infinite ease-in-out;
}

/* Ripple effect for interactions */
@keyframes ripple-effect {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.5);
    opacity: 0.5;
  }
  100% {
    transform: scale(2);
    opacity: 0;
  }
}

.ripple {
  animation: ripple-effect 0.8s ease-out;
}

/* Breathing animation for idle state */
@keyframes breathing {
  0%, 100% {
    transform: scale(1);
    filter: brightness(1);
  }
  50% {
    transform: scale(1.02);
    filter: brightness(1.1);
  }
}

.node-breathing {
  animation: breathing 4s ease-in-out infinite;
}

/* Gradient text effects */
.gradient-text {
  background: linear-gradient(135deg, #4F46E5 0%, #7C3AED 35%, #BE185D 70%, #DC2626 100%);
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
}

/* Enhanced Button styles with modern glass morphism */
.btn-primary {
  padding: 0.5rem 1rem;
  font-weight: 600;
  color: white;
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 0.75rem;
  border: 1px solid rgba(37, 99, 235, 0.3);
  box-shadow:
    0 4px 6px rgba(0, 0, 0, 0.1),
    0 1px 3px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  background: linear-gradient(135deg, #2563eb 0%, #8b5cf6 50%, #10b981 100%);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

.btn-primary:hover {
  transform: translateY(-1px);
  border-color: rgba(139, 92, 246, 0.5);
  box-shadow:
    0 10px 25px rgba(37, 99, 235, 0.25),
    0 4px 12px rgba(139, 92, 246, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  background: linear-gradient(135deg, #1d4ed8 0%, #7c3aed 50%, #059669 100%);
}

.btn-primary:active {
  transform: translateY(0);
  box-shadow:
    0 4px 12px rgba(37, 99, 235, 0.2),
    0 2px 6px rgba(139, 92, 246, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.btn-primary:focus {
  outline: none;
  box-shadow:
    0 10px 25px rgba(37, 99, 235, 0.25),
    0 4px 12px rgba(139, 92, 246, 0.15),
    0 0 0 3px rgba(139, 92, 246, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.btn-primary:disabled {
  transform: none;
  cursor: not-allowed;
  opacity: 0.5;
  background: linear-gradient(135deg, #64748b 0%, #475569 50%, #374151 100%);
  border-color: rgba(100, 116, 139, 0.3);
  box-shadow:
    0 2px 4px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.05);
}

.btn-secondary {
  padding: 0.5rem 1rem;
  font-weight: 500;
  transition: all 0.2s;
  border-radius: 0.5rem;
  background: linear-gradient(135deg, rgba(20, 27, 45, 0.95) 0%, rgba(26, 31, 46, 0.9) 50%, rgba(15, 20, 25, 0.95) 100%);
  backdrop-filter: blur(12px);
  border: 1px solid rgba(148, 163, 184, 0.2);
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
  color: #e2e8f0;
}

.btn-secondary:hover {
  background-color: rgba(26, 31, 46, 0.8);
}

.btn-ghost {
  padding: 0.75rem 1rem;
  font-weight: 500;
  transition: all 0.2s;
  border-radius: 0.5rem;
  color: #cbd5e1;
}

.btn-ghost:hover {
  background-color: rgba(26, 31, 46, 0.8);
  color: #e2e8f0;
}

/* Enhanced mobile touch feedback */
@media (max-width: 768px) {
  .touch-feedback {
    position: absolute;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(139, 92, 246, 0.4) 0%, rgba(139, 92, 246, 0.1) 70%, transparent 100%);
    transform: translate(-50%, -50%);
    pointer-events: none;
    z-index: 100;
    animation: touch-ripple-enhanced 1s ease-out;
  }

/* Search button specific animations */
@keyframes search-glow {
  0% { box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1), 0 1px 3px rgba(0, 0, 0, 0.08); }
  50% { box-shadow: 0 10px 25px rgba(37, 99, 235, 0.25), 0 4px 12px rgba(139, 92, 246, 0.15); }
  100% { box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1), 0 1px 3px rgba(0, 0, 0, 0.08); }
}

@keyframes search-pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

.btn-search-animate {
  animation: search-glow 2s ease-in-out infinite;
}

.btn-search-pulse {
  animation: search-pulse 0.3s ease-out;
}

  @keyframes touch-ripple-enhanced {
    0% {
      transform: translate(-50%, -50%) scale(0.3);
      opacity: 1;
    }
    50% {
      transform: translate(-50%, -50%) scale(1.2);
      opacity: 0.6;
    }
    100% {
      transform: translate(-50%, -50%) scale(2);
      opacity: 0;
    }
  }
}

/* Enhanced Scrollbar styling with responsive and touch support */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background-color: rgba(15, 20, 25, 0.8);
  border-radius: 9999px;
  backdrop-filter: blur(8px);
}

::-webkit-scrollbar-thumb {
  border-radius: 9999px;
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.4) 0%, rgba(168, 85, 247, 0.3) 100%);
  border: 1px solid rgba(139, 92, 246, 0.1);
  transition: all 0.2s ease-in-out;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.7) 0%, rgba(168, 85, 247, 0.6) 100%);
  border-color: rgba(139, 92, 246, 0.3);
  transform: scale(1.1);
}

::-webkit-scrollbar-thumb:active {
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.9) 0%, rgba(168, 85, 247, 0.8) 100%);
  border-color: rgba(139, 92, 246, 0.5);
}

/* Corner styling for horizontal and vertical scrollbars */
::-webkit-scrollbar-corner {
  background-color: rgba(15, 20, 25, 0.8);
  border-radius: 9999px;
}

/* Enhanced scrollable container classes */
.scrollable-container {
  scrollbar-width: thin;
  scrollbar-color: rgba(139, 92, 246, 0.4) rgba(15, 20, 25, 0.8);
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch; /* iOS momentum scrolling */
}

.scrollable-container::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.scrollable-container::-webkit-scrollbar-track {
  background: rgba(15, 20, 25, 0.6);
  border-radius: 9999px;
  margin: 2px;
}

.scrollable-container::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.5) 0%, rgba(168, 85, 247, 0.4) 100%);
  border-radius: 9999px;
  border: 1px solid rgba(139, 92, 246, 0.2);
  transition: all 0.3s ease-in-out;
}

.scrollable-container::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.8) 0%, rgba(168, 85, 247, 0.7) 100%);
  border-color: rgba(139, 92, 246, 0.4);
  box-shadow: 0 0 8px rgba(139, 92, 246, 0.3);
}

/* Mobile-specific scrolling enhancements */
@media (max-width: 768px) {
  .scrollable-container {
    /* Enhanced touch scrolling for mobile */
    -webkit-overflow-scrolling: touch;
    overscroll-behavior: contain;
    scroll-snap-type: y proximity;
  }

  /* Larger scrollbars for touch devices */
  .scrollable-container::-webkit-scrollbar {
    width: 12px;
    height: 12px;
  }

  .scrollable-container::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, rgba(139, 92, 246, 0.6) 0%, rgba(168, 85, 247, 0.5) 100%);
    border: 2px solid rgba(139, 92, 246, 0.2);
    min-height: 40px; /* Minimum touch target size */
  }

  /* Hide scrollbars on mobile by default, show on scroll */
  .scrollable-container:not(:hover):not(:focus):not(:active) {
    scrollbar-width: none;
  }

  .scrollable-container:not(:hover):not(:focus):not(:active)::-webkit-scrollbar {
    display: none;
  }
}

/* Table-specific scrolling styles */
.table-container {
  overflow-x: auto;
  overflow-y: auto;
  max-height: 400px;
  border-radius: 0.75rem;
  border: 1px solid rgba(148, 163, 184, 0.1);
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.95) 0%, rgba(30, 41, 59, 0.9) 50%, rgba(15, 23, 42, 0.95) 100%);
  backdrop-filter: blur(16px);
  scrollbar-width: thin;
  scrollbar-color: rgba(139, 92, 246, 0.4) rgba(15, 20, 25, 0.8);
}

.table-container::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.table-container::-webkit-scrollbar-track {
  background: rgba(15, 20, 25, 0.6);
  border-radius: 9999px;
}

.table-container::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.5) 0%, rgba(168, 85, 247, 0.4) 100%);
  border-radius: 9999px;
  border: 1px solid rgba(139, 92, 246, 0.2);
}

.table-container::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.8) 0%, rgba(168, 85, 247, 0.7) 100%);
  box-shadow: 0 0 6px rgba(139, 92, 246, 0.4);
}

/* List item scrolling with smooth animations */
.scrollable-list {
  scroll-behavior: smooth;
  scroll-padding-top: 1rem;
  scroll-padding-bottom: 1rem;
}

.scrollable-list-item {
  scroll-margin-top: 0.5rem;
  scroll-margin-bottom: 0.5rem;
  transition: all 0.2s ease-in-out;
}

.scrollable-list-item:hover {
  transform: translateX(2px);
  box-shadow: 0 2px 8px rgba(139, 92, 246, 0.2);
}

/* Enhanced LeftSidebar Panel Scrolling */
.sidebar-panel-container {
  scroll-behavior: smooth;
  scroll-padding-top: 1rem;
  scroll-padding-bottom: 1rem;
  scrollbar-width: thin;
  scrollbar-color: rgba(139, 92, 246, 0.5) rgba(15, 20, 25, 0.9);
  -webkit-overflow-scrolling: touch;
  overscroll-behavior: contain;
  position: relative; /* Ensure relative positioning for scroll-to-top buttons */
}

/* Enhanced Scroll-to-top button styles for sidebar panels - Fixed positioning */
.sidebar-scroll-to-top {
  position: absolute;
  bottom: 1rem;
  right: 1rem;
  z-index: 60;
  width: 44px;
  height: 44px;
  padding: 0;
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.95) 0%, rgba(168, 85, 247, 0.9) 100%);
  color: white;
  border: none;
  border-radius: 50%;
  box-shadow: 0 4px 16px rgba(139, 92, 246, 0.4);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  backdrop-filter: blur(12px);
  border: 1px solid rgba(139, 92, 246, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  /* Ensure minimum touch target size */
  min-width: 44px;
  min-height: 44px;
  /* Prevent button from scrolling with content */
  pointer-events: auto;
}

.sidebar-scroll-to-top:hover {
  background: linear-gradient(135deg, rgba(139, 92, 246, 1) 0%, rgba(168, 85, 247, 1) 100%);
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 8px 24px rgba(139, 92, 246, 0.5);
  border-color: rgba(139, 92, 246, 0.5);
}

.sidebar-scroll-to-top:active {
  transform: translateY(0) scale(0.98);
  box-shadow: 0 2px 8px rgba(139, 92, 246, 0.3);
}

.sidebar-scroll-to-top:focus {
  outline: none;
  box-shadow: 0 4px 16px rgba(139, 92, 246, 0.4), 0 0 0 3px rgba(139, 92, 246, 0.2);
}

/* Responsive adjustments for mobile and sidebar contexts */
@media (max-width: 768px) {
  .sidebar-scroll-to-top {
    width: 48px;
    height: 48px;
    min-width: 48px;
    min-height: 48px;
    bottom: 1.5rem;
    right: 1.5rem;
    font-size: 18px;
  }
}

/* Special positioning for mobile bottom sheet layout */
.mobile-layout .sidebar-scroll-to-top {
  bottom: 2rem;
  right: 1.5rem;
}

/* Ensure button is fixed within sidebar container - NOT scrollable */
.sidebar-panel-container .sidebar-scroll-to-top {
  /* Position fixed relative to the nearest positioned ancestor (sidebar container) */
  position: fixed;
  bottom: 1rem;
  right: 1rem;
  /* Ensure it's above content but below modals */
  z-index: 55;
  /* Prevent the button from scrolling with content */
  pointer-events: auto;
}

/* Adjust positioning when sidebar has tab navigation */
.sidebar-panel-container.has-tabs .sidebar-scroll-to-top {
  bottom: 1.5rem; /* Extra space to avoid tab area */
}

/* Animation states */
.sidebar-scroll-to-top.fade-in {
  opacity: 1;
  transform: translateY(0);
}

.sidebar-scroll-to-top.fade-out {
  opacity: 0;
  transform: translateY(20px);
  pointer-events: none;
}

.sidebar-panel-container::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.sidebar-panel-container::-webkit-scrollbar-track {
  background: rgba(15, 20, 25, 0.9);
  border-radius: 9999px;
  margin: 4px 0;
  backdrop-filter: blur(12px);
}

.sidebar-panel-container::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.6) 0%, rgba(168, 85, 247, 0.5) 100%);
  border-radius: 9999px;
  border: 1px solid rgba(139, 92, 246, 0.3);
  transition: all 0.3s ease-in-out;
  min-height: 30px;
}

.sidebar-panel-container::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.9) 0%, rgba(168, 85, 247, 0.8) 100%);
  border-color: rgba(139, 92, 246, 0.5);
  box-shadow: 0 0 12px rgba(139, 92, 246, 0.4);
  transform: scaleY(1.1);
}

.sidebar-panel-container::-webkit-scrollbar-thumb:active {
  background: linear-gradient(135deg, rgba(139, 92, 246, 1) 0%, rgba(168, 85, 247, 0.9) 100%);
  border-color: rgba(139, 92, 246, 0.7);
}

/* Security Alerts specific scrolling */
.security-alerts-list {
  scroll-snap-type: y proximity;
  scroll-padding: 0.5rem;
}

.security-alert-item {
  scroll-snap-align: start;
  scroll-margin: 0.5rem 0;
  transition: all 0.3s ease-in-out;
}

.security-alert-item:hover {
  transform: translateX(3px);
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.2);
}

/* Transaction Flow specific scrolling */
.transaction-flow-list {
  scroll-snap-type: y proximity;
  scroll-padding: 0.75rem;
  position: relative;
}

.transaction-item {
  scroll-snap-align: start;
  scroll-margin: 0.75rem 0;
  transition: all 0.3s ease-in-out;
  position: relative;
}

.transaction-item:hover {
  transform: translateX(3px);
  box-shadow: 0 4px 12px rgba(37, 99, 235, 0.2);
}

.transaction-item.selected {
  transform: translateX(5px);
  box-shadow: 0 6px 16px rgba(139, 92, 246, 0.3);
  border-color: rgba(139, 92, 246, 0.5);
}

/* Scroll indicators */
.scroll-indicator {
  position: absolute;
  right: 4px;
  width: 2px;
  background: linear-gradient(to bottom, transparent 0%, rgba(139, 92, 246, 0.3) 20%, rgba(139, 92, 246, 0.3) 80%, transparent 100%);
  border-radius: 1px;
  opacity: 0;
  transition: opacity 0.3s ease-in-out;
  pointer-events: none;
}

.sidebar-panel-container:hover .scroll-indicator {
  opacity: 1;
}

/* Mobile optimizations for sidebar panels */
@media (max-width: 640px) {
  .sidebar-panel-container {
    scroll-snap-type: y mandatory;
    overscroll-behavior: contain;
  }

  .sidebar-panel-container::-webkit-scrollbar {
    width: 14px;
    height: 14px;
  }

  .sidebar-panel-container::-webkit-scrollbar-thumb {
    min-height: 44px; /* iOS touch target minimum */
    background: linear-gradient(135deg, rgba(139, 92, 246, 0.8) 0%, rgba(168, 85, 247, 0.7) 100%);
  }

  /* Hide scrollbars on mobile when not actively scrolling */
  .sidebar-panel-container:not(:hover):not(:focus):not(:active)::-webkit-scrollbar {
    width: 0px;
  }

  .security-alert-item,
  .transaction-item {
    scroll-snap-align: center;
    scroll-margin: 1rem 0;
  }
}

/* Enhanced Tooltip Scrolling Styles */
.tooltip-container {
  max-height: min(300px, 70vh);
  overflow-y: auto;
  overflow-x: hidden;
  scrollbar-width: thin;
  scrollbar-color: rgba(139, 92, 246, 0.5) rgba(15, 20, 25, 0.6);
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
  overscroll-behavior: contain;
  /* Enhanced padding for better content spacing */
  padding-right: 4px;
}

.tooltip-container::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.tooltip-container::-webkit-scrollbar-track {
  background: rgba(15, 20, 25, 0.6);
  border-radius: 9999px;
  margin: 2px 0;
}

.tooltip-container::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.6) 0%, rgba(168, 85, 247, 0.5) 100%);
  border-radius: 9999px;
  border: 1px solid rgba(139, 92, 246, 0.2);
  transition: all 0.2s ease-in-out;
  min-height: 20px;
}

.tooltip-container::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.8) 0%, rgba(168, 85, 247, 0.7) 100%);
  border-color: rgba(139, 92, 246, 0.4);
  box-shadow: 0 0 6px rgba(139, 92, 246, 0.3);
}

/* Mobile-specific tooltip scrolling */
@media (max-width: 640px) {
  .tooltip-container {
    max-height: min(250px, 60vh);
    overscroll-behavior: contain;
  }

  .tooltip-container::-webkit-scrollbar {
    width: 8px;
  }

  .tooltip-container::-webkit-scrollbar-thumb {
    min-height: 30px;
    background: linear-gradient(135deg, rgba(139, 92, 246, 0.7) 0%, rgba(168, 85, 247, 0.6) 100%);
  }

  /* Hide scrollbars on mobile when not actively scrolling */
  .tooltip-container:not(:hover):not(:focus):not(:active)::-webkit-scrollbar {
    width: 0px;
  }
}

/* Enhanced Modal Content Scrolling Styles */
.modal-content-scrollable {
  max-height: 400px;
  overflow-y: auto;
  overflow-x: hidden;
  scrollbar-width: thin;
  scrollbar-color: rgba(139, 92, 246, 0.5) rgba(15, 20, 25, 0.8);
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
  overscroll-behavior: contain;
  border-radius: 0.5rem;
  background: rgba(15, 20, 25, 0.3);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(148, 163, 184, 0.1);
}

.modal-content-scrollable::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.modal-content-scrollable::-webkit-scrollbar-track {
  background: rgba(15, 20, 25, 0.6);
  border-radius: 9999px;
  margin: 2px 0;
}

.modal-content-scrollable::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.6) 0%, rgba(168, 85, 247, 0.5) 100%);
  border-radius: 9999px;
  border: 1px solid rgba(139, 92, 246, 0.2);
  transition: all 0.3s ease-in-out;
  min-height: 30px;
}

.modal-content-scrollable::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.8) 0%, rgba(168, 85, 247, 0.7) 100%);
  border-color: rgba(139, 92, 246, 0.4);
  box-shadow: 0 0 8px rgba(139, 92, 246, 0.3);
}

/* Specific scrollable sections within modals */
.modal-section-scrollable {
  max-height: 200px;
  overflow-y: auto;
  overflow-x: hidden;
  scrollbar-width: thin;
  scrollbar-color: rgba(139, 92, 246, 0.4) rgba(15, 20, 25, 0.6);
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
  overscroll-behavior: contain;
  padding: 0.5rem;
  margin: 0.25rem 0;
  border-radius: 0.375rem;
  background: rgba(15, 20, 25, 0.2);
  border: 1px solid rgba(148, 163, 184, 0.05);
}

.modal-section-scrollable::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.modal-section-scrollable::-webkit-scrollbar-track {
  background: rgba(15, 20, 25, 0.4);
  border-radius: 9999px;
  margin: 1px 0;
}

.modal-section-scrollable::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.5) 0%, rgba(168, 85, 247, 0.4) 100%);
  border-radius: 9999px;
  border: 1px solid rgba(139, 92, 246, 0.1);
  transition: all 0.2s ease-in-out;
  min-height: 20px;
}

.modal-section-scrollable::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.7) 0%, rgba(168, 85, 247, 0.6) 100%);
  border-color: rgba(139, 92, 246, 0.3);
  box-shadow: 0 0 6px rgba(139, 92, 246, 0.2);
}

/* Mobile optimizations for modal scrolling */
@media (max-width: 640px) {
  .modal-content-scrollable {
    max-height: min(300px, 50vh);
    overscroll-behavior: contain;
  }

  .modal-content-scrollable::-webkit-scrollbar {
    width: 10px;
  }

  .modal-content-scrollable::-webkit-scrollbar-thumb {
    min-height: 40px;
    background: linear-gradient(135deg, rgba(139, 92, 246, 0.7) 0%, rgba(168, 85, 247, 0.6) 100%);
  }

  .modal-section-scrollable {
    max-height: min(150px, 30vh);
  }

  .modal-section-scrollable::-webkit-scrollbar {
    width: 8px;
  }

  .modal-section-scrollable::-webkit-scrollbar-thumb {
    min-height: 30px;
    background: linear-gradient(135deg, rgba(139, 92, 246, 0.6) 0%, rgba(168, 85, 247, 0.5) 100%);
  }

  /* Hide scrollbars on mobile when not actively scrolling */
  .modal-content-scrollable:not(:hover):not(:focus):not(:active)::-webkit-scrollbar,
  .modal-section-scrollable:not(:hover):not(:focus):not(:active)::-webkit-scrollbar {
    width: 0px;
  }
}

/* Scroll fade indicators for better UX */
.modal-content-scrollable::before,
.modal-content-scrollable::after {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  height: 20px;
  pointer-events: none;
  z-index: 10;
  transition: opacity 0.3s ease-in-out;
}

.modal-content-scrollable::before {
  top: 0;
  background: linear-gradient(to bottom, rgba(15, 20, 25, 0.8) 0%, transparent 100%);
  opacity: 0;
}

.modal-content-scrollable::after {
  bottom: 0;
  background: linear-gradient(to top, rgba(15, 20, 25, 0.8) 0%, transparent 100%);
  opacity: 0;
}

.modal-content-scrollable.has-scroll-top::before {
  opacity: 1;
}

.modal-content-scrollable.has-scroll-bottom::after {
  opacity: 1;
}

/* Enhanced focus states for accessibility */
.modal-content-scrollable:focus-visible,
.modal-section-scrollable:focus-visible {
  outline: 2px solid rgba(139, 92, 246, 0.6);
  outline-offset: 2px;
}

/* Keyboard navigation support */
.modal-content-scrollable,
.modal-section-scrollable {
  /* Make scrollable areas focusable for keyboard navigation */
  outline: none;
}

.modal-content-scrollable:focus,
.modal-section-scrollable:focus {
  outline: 1px solid rgba(139, 92, 246, 0.4);
  outline-offset: 1px;
}

/* Smooth scroll behavior for all scrollable elements */
.scrollable-container,
.modal-content-scrollable,
.modal-section-scrollable,
.sidebar-panel-container {
  scroll-behavior: smooth;
}

/* Enhanced scroll snap for better mobile experience */
@media (max-width: 768px) {
  .modal-content-scrollable {
    scroll-snap-type: y proximity;
  }

  .modal-section-scrollable {
    scroll-snap-type: y proximity;
  }

  .modal-content-scrollable > * {
    scroll-snap-align: start;
    scroll-margin: 1rem 0;
  }
}

/* Tooltip content styling for better readability */
.tooltip-content {
  line-height: 1.5;
  word-wrap: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
}

.tooltip-content.vietnamese-text {
  word-break: break-word;
  overflow-wrap: break-word;
  line-height: 1.6;
}

/* Enhanced tooltip positioning for center-right map area */
.tooltip-center-right {
  position: fixed;
  z-index: 99999; /* Highest z-index - above TopBar (z-50) and all other components */
  pointer-events: none;
  max-width: min(320px, 90vw);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  /* Enhanced glass morphism effect */
  background: linear-gradient(135deg,
    rgba(15, 23, 42, 0.9) 0%,
    rgba(30, 41, 59, 0.85) 50%,
    rgba(15, 23, 42, 0.9) 100%);
  /* Subtle glow effect */
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.4),
    0 0 20px rgba(139, 92, 246, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  /* Smooth animations */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  /* Ensure tooltip appears above TopBar */
  transform-origin: center;
}

@media (max-width: 640px) {
  .tooltip-center-right {
    max-width: min(280px, 85vw);
    backdrop-filter: blur(16px);
    -webkit-backdrop-filter: blur(16px);
  }
}

/* Loading animations */
.loading-spinner {
  border: 2px solid rgba(148, 163, 184, 0.1);
  border-top: 2px solid #a855f7;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-dots::after {
  content: '';
  animation: loading-dots 1.5s infinite;
}

@keyframes loading-dots {
  0%, 20% { content: ''; }
  40% { content: '.'; }
  60% { content: '..'; }
  80%, 100% { content: '...'; }
}

/* Slide down animation for notifications */
@keyframes slide-down {
  0% {
    transform: translate(-50%, -100%);
    opacity: 0;
  }
  100% {
    transform: translate(-50%, 0);
    opacity: 1;
  }
}

.animate-slide-down {
  animation: slide-down 0.3s ease-out;
}

/* Focus styles */
.focus-ring:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(168, 85, 247, 0.5), 0 0 0 4px #0a0e1a;
}

/* Selection styles */
::selection {
  background-color: rgba(168, 85, 247, 0.3);
  color: #f1f5f9;
}

/* Performance Monitor Dropdown Animations */
@keyframes slide-up {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes slide-down-expand {
  from {
    opacity: 0;
    max-height: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    max-height: 200px;
    transform: translateY(0);
  }
}

.animate-slide-up {
  animation: slide-up 0.3s ease-out;
}

.animate-slide-down {
  animation: slide-down-expand 0.3s ease-out;
}

/* Responsive Text and Layout Utilities */
.text-responsive {
  overflow-wrap: break-word;
  word-wrap: break-word;
  hyphens: auto;
}

.container-responsive {
  max-width: 100%;
  overflow-x: hidden;
}

.grid-responsive {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

@media (max-width: 475px) {
  .grid-responsive {
    grid-template-columns: 1fr;
  }
}

/* Prevent horizontal overflow on all elements */
* {
  max-width: 100%;
  box-sizing: border-box;
}

/* Enhanced Vietnamese Language Support */
.vietnamese-text {
  word-break: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
  line-height: 1.6; /* Increased for better readability */
  font-feature-settings: "kern" 1, "liga" 1; /* Better typography */
}

/* Vietnamese text in tooltips */
.vietnamese-text.tooltip-content {
  max-width: 100%;
  white-space: normal;
  word-wrap: break-word;
  overflow-wrap: anywhere;
}

/* Vietnamese text in buttons and labels */
.vietnamese-text.button-label {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  max-width: 100%;
}

/* Vietnamese text in panels */
.vietnamese-text.panel-text {
  word-break: break-word;
  overflow-wrap: break-word;
  max-width: 100%;
  line-height: 1.5;
}

/* Vietnamese text in mobile layouts */
@media (max-width: 767px) {
  .vietnamese-text {
    font-size: 0.9rem; /* Slightly smaller on mobile for better fit */
    line-height: 1.5;
  }

  .vietnamese-text.tooltip-content {
    font-size: 0.8rem;
    line-height: 1.4;
  }
}

/* Vietnamese text truncation utilities */
.vietnamese-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%;
}

.vietnamese-truncate-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-break: break-word;
}

.vietnamese-truncate-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-break: break-word;
}

/* Vietnamese text in specific components */
.vietnamese-text.sidebar-label {
  max-width: calc(100% - 2rem);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.vietnamese-text.control-panel-label {
  font-size: 0.75rem;
  line-height: 1.2;
  text-align: center;
  max-width: 100%;
  word-break: break-word;
}

/* Responsive Vietnamese text sizing */
@media (min-width: 768px) and (max-width: 1023px) {
  .vietnamese-text.tablet-responsive {
    font-size: 0.85rem;
    line-height: 1.4;
  }
}

@media (min-width: 1024px) {
  .vietnamese-text.desktop-responsive {
    font-size: 0.9rem;
    line-height: 1.5;
  }
}

/* Enhanced Responsive Layout System */

/* CSS Grid Layout Utilities */
.layout-grid {
  display: grid;
  grid-template-columns: 1fr;
  grid-template-rows: auto 1fr auto;
  min-height: 100vh;
  overflow-x: hidden;
}

.layout-grid-desktop {
  grid-template-columns: auto 1fr auto;
  grid-template-rows: auto 1fr;
}

.layout-grid-tablet {
  grid-template-columns: auto 1fr;
  grid-template-rows: auto 1fr;
}

/* Flexbox Layout Utilities */
.layout-flex {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  overflow-x: hidden;
}

.layout-flex-desktop {
  flex-direction: row;
}

.layout-flex-tablet {
  flex-direction: row;
}

/* Responsive Container System */
.responsive-container {
  width: 100%;
  max-width: 100vw;
  overflow-x: hidden;
  box-sizing: border-box;
}

.responsive-container.mobile-layout {
  padding: 0.5rem;
}

.responsive-container.tablet-layout {
  padding: 1rem;
}

.responsive-container.desktop-layout {
  padding: 1.5rem;
}

/* Panel Layout System */
.panel-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

.panel-header {
  flex-shrink: 0;
}

.panel-content {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
}

.panel-footer {
  flex-shrink: 0;
}

/* Mobile-First Responsive Utilities */
.mobile-stack {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

@media (min-width: 768px) {
  .mobile-stack.tablet-row {
    flex-direction: row;
  }
}

@media (min-width: 1024px) {
  .mobile-stack.desktop-row {
    flex-direction: row;
  }
}

/* Prevent Horizontal Overflow */
.no-horizontal-scroll {
  overflow-x: hidden;
  max-width: 100vw;
  box-sizing: border-box;
}

.no-horizontal-scroll * {
  max-width: 100%;
  box-sizing: border-box;
}

/* Z-Index Hierarchy for Proper Layering */
/*
  Z-Index Scale (Updated for TopBar compatibility):
  - Tooltips: 9999 (highest - always visible above TopBar)
  - Modals/Overlays: 9000-9999
  - Debug Panel: 60
  - Header/TopBar: 50 (fixed top navigation)
  - Control Panel: 45 (below header, above other panels)
  - LeftSidebar Container: 40 (below header)
  - WalletAnalysisPanel: 35
  - Loading Overlay: 8000 (below tooltips but above all other content)
  - BubbleMapControls: 30
  - Performance Monitor: 30
  - Other floating elements: 20-29
  - Base elements: 1-19

  Note: Tooltips use z-9999 to ensure they appear above the TopBar (z-50)
  and all other UI components without interference.
*/

.tooltip-layer {
  z-index: 9999;
}

.modal-layer {
  z-index: 9000;
}

.overlay-layer {
  z-index: 8000;
}

.floating-panel-layer {
  z-index: 100;
}

.sidebar-layer {
  z-index: 40;
}

.performance-layer {
  z-index: 30;
}

.base-layer {
  z-index: 10;
}

/* Dashboard-specific styling */
.dashboard-card {
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.95) 0%, rgba(30, 41, 59, 0.9) 50%, rgba(15, 23, 42, 0.95) 100%);
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
  border: 1px solid rgba(148, 163, 184, 0.1);
  transition: all 0.3s ease;
}

.dashboard-card:hover {
  border-color: rgba(139, 92, 246, 0.3);
  box-shadow: 0 8px 32px 0 rgba(139, 92, 246, 0.15);
  transform: translateY(-2px);
}

/* Enhanced Wallet Information Panel Styling */
.wallet-info-enhanced {
  /* Optimized spacing for wider panels */
  --panel-padding-sm: 1rem;
  --panel-padding-lg: 1.25rem;
  --panel-padding-xl: 1.5rem;

  /* Enhanced grid layouts */
  --grid-cols-base: 2;
  --grid-cols-lg: 2;
  --grid-cols-xl: 3;
}

/* Responsive content visibility utilities */
.content-enhanced-lg {
  @apply hidden lg:block;
}

.content-enhanced-xl {
  @apply hidden xl:block;
}

/* Improved transaction item layouts */
.transaction-item-enhanced {
  display: grid;
  grid-template-columns: auto 1fr auto;
  gap: 0.75rem;
  align-items: center;
}

@media (min-width: 1024px) {
  .transaction-item-enhanced {
    grid-template-columns: auto 1fr auto auto;
    gap: 1rem;
  }
}

@media (min-width: 1280px) {
  .transaction-item-enhanced {
    grid-template-columns: auto 1fr auto auto auto;
    gap: 1.25rem;
  }
}

/* Resizable Panel Styling */
.resizable-panel {
  position: relative;
  transition: width 0.2s ease-out;
}

.resizable-panel.resizing {
  transition: none;
  user-select: none;
}

.resize-handle {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 8px;
  cursor: col-resize;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.2s ease-in-out;
}

.resize-handle:hover,
.resize-handle.active {
  opacity: 1;
}

.resize-handle.left {
  left: -4px;
}

.resize-handle.right {
  right: -4px;
}

.resize-handle::before {
  content: '';
  width: 2px;
  height: 40px;
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.8) 0%, rgba(168, 85, 247, 0.7) 100%);
  border-radius: 1px;
  transition: all 0.2s ease-in-out;
}

.resize-handle:hover::before,
.resize-handle.active::before {
  height: 60px;
  background: linear-gradient(135deg, rgba(139, 92, 246, 1) 0%, rgba(168, 85, 247, 0.9) 100%);
  box-shadow: 0 0 10px rgba(139, 92, 246, 0.5);
}

/* Prevent text selection during resize */
.resizing * {
  user-select: none !important;
  pointer-events: none !important;
}

/* Smooth width transitions for panels */
.panel-width-transition {
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Resize cursor for the entire document during resize */
body.resizing {
  cursor: col-resize !important;
}

body.resizing * {
  cursor: col-resize !important;
}

/* Wallet type color indicators */
.wallet-regular { color: #2563EB; }
.wallet-exchange { color: #10B981; }
.wallet-contract { color: #8B5CF6; }
.wallet-whale { color: #06B6D4; }
.wallet-defi { color: #EC4899; }
.wallet-bridge { color: #F59E0B; }
.wallet-miner { color: #EF4444; }

/* Risk level indicators */
.risk-low { color: #10B981; }
.risk-medium { color: #F59E0B; }
.risk-high { color: #F97316; }
.risk-critical { color: #EF4444; }

/* Enhanced loading animations */
.loading-pulse {
  animation: loading-pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes loading-pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Smooth hover transitions for interactive elements */
.interactive-hover {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.interactive-hover:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Dashboard chart animations */
@keyframes chart-bar-grow {
  0% {
    width: 0%;
  }
  100% {
    width: var(--target-width);
  }
}

.chart-bar {
  animation: chart-bar-grow 1s ease-out;
  animation-fill-mode: forwards;
}

/* Dashboard metric card animations */
@keyframes metric-card-slide-up {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.metric-card {
  animation: metric-card-slide-up 0.6s ease-out;
  animation-fill-mode: forwards;
}

.metric-card:nth-child(1) { animation-delay: 0.1s; }
.metric-card:nth-child(2) { animation-delay: 0.2s; }
.metric-card:nth-child(3) { animation-delay: 0.3s; }
.metric-card:nth-child(4) { animation-delay: 0.4s; }

/* Dashboard ranking animations */
@keyframes ranking-item-fade-in {
  0% {
    opacity: 0;
    transform: translateX(-20px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

.ranking-item {
  animation: ranking-item-fade-in 0.5s ease-out;
  animation-fill-mode: forwards;
}

.ranking-item:nth-child(1) { animation-delay: 0.1s; }
.ranking-item:nth-child(2) { animation-delay: 0.15s; }
.ranking-item:nth-child(3) { animation-delay: 0.2s; }
.ranking-item:nth-child(4) { animation-delay: 0.25s; }
.ranking-item:nth-child(5) { animation-delay: 0.3s; }

/* Enhanced loading animations */
@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.animate-shimmer {
  animation: shimmer 2s infinite;
}

/* Skeleton loading with breathing effect */
@keyframes skeleton-loading {
  0%, 100% {
    opacity: 0.4;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.02);
  }
}

.skeleton-loading {
  animation: skeleton-loading 2s ease-in-out infinite;
}

/* Staggered fade-in animation for loading items */
@keyframes stagger-fade-in {
  0% {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.stagger-fade-in {
  animation: stagger-fade-in 0.6s ease-out forwards;
}

/* Pulse with gradient effect */
@keyframes gradient-pulse {
  0%, 100% {
    background-position: 0% 50%;
    opacity: 0.6;
  }
  50% {
    background-position: 100% 50%;
    opacity: 1;
  }
}

.gradient-pulse {
  background: linear-gradient(90deg,
    rgba(37, 99, 235, 0.1) 0%,
    rgba(139, 92, 246, 0.2) 25%,
    rgba(236, 72, 153, 0.2) 50%,
    rgba(245, 158, 11, 0.2) 75%,
    rgba(16, 185, 129, 0.1) 100%);
  background-size: 200% 200%;
  animation: gradient-pulse 3s ease-in-out infinite;
}

/* Loading dots animation */
@keyframes loading-dots {
  0%, 20% {
    opacity: 0;
    transform: scale(0.8);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
  100% {
    opacity: 0;
    transform: scale(0.8);
  }
}

.loading-dots {
  animation: loading-dots 1.5s ease-in-out infinite;
}

.loading-dots:nth-child(1) { animation-delay: 0s; }
.loading-dots:nth-child(2) { animation-delay: 0.3s; }
.loading-dots:nth-child(3) { animation-delay: 0.6s; }

/* Enhanced loading state transitions */
@keyframes fade-in-up {
  0% {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.fade-in-up {
  animation: fade-in-up 0.8s ease-out forwards;
}

/* Smooth loading to content transition */
@keyframes content-reveal {
  0% {
    opacity: 0;
    transform: translateY(20px);
    filter: blur(4px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
    filter: blur(0);
  }
}

.content-reveal {
  animation: content-reveal 0.6s ease-out forwards;
}

/* Loading state overlay */
@keyframes loading-overlay {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  100% {
    opacity: 0;
    transform: scale(1.05);
  }
}

.loading-overlay-exit {
  animation: loading-overlay 0.5s ease-out forwards;
}

/* Ranking item entrance animation */
.ranking-item {
  opacity: 0;
  animation: ranking-item-fade-in 0.6s ease-out forwards;
}

/* Improved shimmer effect */
@keyframes shimmer-enhanced {
  0% {
    transform: translateX(-100%) skewX(-15deg);
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    transform: translateX(200%) skewX(-15deg);
    opacity: 0;
  }
}

.animate-shimmer {
  animation: shimmer-enhanced 2.5s ease-in-out infinite;
}

/* Smooth ranking item entrance */
@keyframes ranking-item-smooth-entrance {
  0% {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
    filter: blur(4px);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
    filter: blur(0);
  }
}

.ranking-item-smooth {
  opacity: 0;
  animation: ranking-item-smooth-entrance 0.8s ease-out forwards;
}

/* Enhanced transition states */
.transition-content-enter {
  opacity: 0;
  transform: translateY(20px) scale(0.98);
  filter: blur(2px);
}

.transition-content-enter-active {
  opacity: 1;
  transform: translateY(0) scale(1);
  filter: blur(0);
  transition: all 0.6s ease-out;
}

.transition-content-exit {
  opacity: 1;
  transform: translateY(0) scale(1);
  filter: blur(0);
}

.transition-content-exit-active {
  opacity: 0;
  transform: translateY(-10px) scale(1.02);
  filter: blur(2px);
  transition: all 0.4s ease-in;
}

/* Breathing overlay effect */
@keyframes breathing-overlay {
  0%, 100% {
    opacity: 0.8;
    backdrop-filter: blur(2px);
  }
  50% {
    opacity: 0.6;
    backdrop-filter: blur(4px);
  }
}

.breathing-overlay {
  animation: breathing-overlay 2s ease-in-out infinite;
}

/* Smooth crossfade transition */
@keyframes crossfade-in {
  0% {
    opacity: 0;
    transform: translateY(20px) scale(0.98);
    filter: blur(3px);
  }
  50% {
    opacity: 0.5;
    transform: translateY(10px) scale(0.99);
    filter: blur(1px);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
    filter: blur(0);
  }
}

.crossfade-in {
  animation: crossfade-in 0.8s ease-out forwards;
}

@keyframes crossfade-out {
  0% {
    opacity: 1;
    transform: translateY(0) scale(1);
    filter: blur(0);
  }
  50% {
    opacity: 0.5;
    transform: translateY(-10px) scale(1.01);
    filter: blur(1px);
  }
  100% {
    opacity: 0;
    transform: translateY(-20px) scale(1.02);
    filter: blur(3px);
  }
}

.crossfade-out {
  animation: crossfade-out 0.6s ease-in forwards;
}

/* Enhanced CTA Button Animations */
@keyframes cta-glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(37, 99, 235, 0.3), 0 0 40px rgba(139, 92, 246, 0.2);
  }
  50% {
    box-shadow: 0 0 30px rgba(37, 99, 235, 0.5), 0 0 60px rgba(139, 92, 246, 0.3);
  }
}

.cta-glow {
  animation: cta-glow 3s ease-in-out infinite;
}

@keyframes cta-pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

.cta-pulse {
  animation: cta-pulse 2s ease-in-out infinite;
}

/* Floating button animations */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.float-animation {
  animation: float 3s ease-in-out infinite;
}

@keyframes bounce-subtle {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-5px);
  }
}

.bounce-subtle {
  animation: bounce-subtle 2s ease-in-out infinite;
}

/* Banner slide-in animation */
@keyframes slide-down {
  0% {
    transform: translateY(-100%);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

.slide-down {
  animation: slide-down 0.5s ease-out;
}

/* CTA button hover effects */
.cta-button {
  position: relative;
  overflow: hidden;
}

.cta-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.cta-button:hover::before {
  left: 100%;
}

/* Magnetic effect for CTA buttons */
@keyframes magnetic-pull {
  0% {
    transform: scale(1) rotate(0deg);
  }
  25% {
    transform: scale(1.02) rotate(1deg);
  }
  50% {
    transform: scale(1.05) rotate(0deg);
  }
  75% {
    transform: scale(1.02) rotate(-1deg);
  }
  100% {
    transform: scale(1) rotate(0deg);
  }
}

.magnetic-hover:hover {
  animation: magnetic-pull 0.6s ease-in-out;
}

/* Network Selector Animations */
@keyframes network-dropdown-slide {
  0% {
    opacity: 0;
    transform: translateY(-10px) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.network-dropdown {
  animation: network-dropdown-slide 0.3s ease-out;
}

@keyframes network-icon-bounce {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

.network-icon:hover {
  animation: network-icon-bounce 0.4s ease-in-out;
}





@keyframes network-glow-pulse {
  0%, 100% {
    box-shadow: 0 0 10px rgba(59, 130, 246, 0.3);
  }
  50% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.5), 0 0 30px rgba(139, 92, 246, 0.3);
  }
}

.network-glow {
  animation: network-glow-pulse 2s ease-in-out infinite;
}

/* Network Category Badges */
@keyframes category-badge-glow {
  0%, 100% {
    box-shadow: 0 0 5px currentColor;
  }
  50% {
    box-shadow: 0 0 15px currentColor, 0 0 25px currentColor;
  }
}

.category-badge:hover {
  animation: category-badge-glow 1s ease-in-out infinite;
}

/* Network Statistics Cards */
@keyframes network-stat-float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-5px);
  }
}

.network-stat-card:hover {
  animation: network-stat-float 2s ease-in-out infinite;
}

/* Network Search Input */
@keyframes search-focus-glow {
  0% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.4);
  }
  100% {
    box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
  }
}

.search-input:focus {
  animation: search-focus-glow 0.3s ease-out;
}

/* Network Metrics Animation */
@keyframes metric-counter {
  0% {
    transform: scale(0.8);
    opacity: 0;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.metric-animate {
  animation: metric-counter 0.8s ease-out;
}

/* Network Connection Lines */
@keyframes connection-pulse {
  0%, 100% {
    opacity: 0.3;
    stroke-width: 1;
  }
  50% {
    opacity: 0.8;
    stroke-width: 2;
  }
}

.connection-line {
  animation: connection-pulse 3s ease-in-out infinite;
}

/* Network Loading States */
@keyframes network-skeleton-wave {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.network-skeleton {
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  background-size: 200px 100%;
  animation: network-skeleton-wave 1.5s ease-in-out infinite;
}

/* Network Selector Row Layout */
.network-selector-row {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  align-items: flex-start;
}

@media (min-width: 1280px) {
  .network-selector-row {
    flex-direction: row;
    gap: 1.5rem;
    align-items: center;
  }
}

/* Network Indicator Responsive */
.network-indicator {
  width: 100%;
  display: flex;
  justify-content: center;
}

@media (min-width: 1280px) {
  .network-indicator {
    width: auto;
    justify-content: flex-start;
  }
}

/* Network Selector Responsive Width */
.network-selector-container {
  width: 100%;
  flex: 1;
}

@media (min-width: 1280px) {
  .network-selector-container {
    max-width: 32rem; /* max-w-2xl */
  }
}

/* Compact Network Selector Styling */
.max-w-xs {
  max-width: 20rem; /* 320px */
}

.max-w-sm {
  max-width: 24rem; /* 384px */
}

/* Ensure NetworkSelector dropdown matches analyzing indicator size */
@media (min-width: 640px) {
  .max-w-xs {
    max-width: 22rem; /* 352px - slightly larger on sm+ screens */
  }

  .max-w-sm {
    max-width: 26rem; /* 416px - slightly larger on sm+ screens */
  }
}

/* Network Analysis Section Smooth Scroll */
#network-analysis {
  scroll-margin-top: 2rem;
}

@media (min-width: 768px) {
  #network-analysis {
    scroll-margin-top: 3rem;
  }
}

/* Network Glow Enhanced */
.network-glow-enhanced {
  position: relative;
}

.network-glow-enhanced::before {
  content: '';
  position: absolute;
  inset: -2px;
  border-radius: inherit;
  padding: 2px;
  background: linear-gradient(45deg,
    rgba(59, 130, 246, 0.3),
    rgba(139, 92, 246, 0.3),
    rgba(59, 130, 246, 0.3)
  );
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: xor;
  animation: network-glow-pulse 2s ease-in-out infinite;
}

/* Compact Network Indicator for Mobile */
@media (max-width: 640px) {
  .network-indicator-compact {
    padding: 0.5rem 0.75rem;
    gap: 0.5rem;
  }

  .network-indicator-compact .network-icon {
    width: 1.25rem;
    height: 1.25rem;
    font-size: 0.75rem;
  }

  .network-indicator-compact .network-name {
    font-size: 0.75rem;
  }
}

/* Network Selector Full Width on Mobile */
@media (max-width: 1279px) {
  .network-selector-mobile-full {
    width: 100%;
    margin-bottom: 1rem;
  }
}

/* Smooth Layout Transitions */
.layout-transition {
  transition: all 0.3s ease-in-out;
}

.layout-transition-fast {
  transition: all 0.2s ease-out;
}

/* Analysis Section Separator */
@keyframes separator-glow {
  0%, 100% {
    opacity: 0.5;
  }
  50% {
    opacity: 1;
  }
}

.analysis-separator {
  animation: separator-glow 3s ease-in-out infinite;
}

.analysis-separator .separator-line {
  background: linear-gradient(90deg,
    transparent,
    rgba(59, 130, 246, 0.3),
    rgba(139, 92, 246, 0.3),
    rgba(59, 130, 246, 0.3),
    transparent
  );
  animation: separator-glow 4s ease-in-out infinite;
}

/* Separator Badge Animation */
@keyframes separator-badge-float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-2px);
  }
}

.separator-badge {
  animation: separator-badge-float 3s ease-in-out infinite;
}

/* Subtle Visual Separator */
@keyframes subtle-separator-glow {
  0%, 100% {
    opacity: 0.3;
    transform: scaleX(0.8);
  }
  50% {
    opacity: 0.7;
    transform: scaleX(1);
  }
}

.subtle-separator {
  animation: subtle-separator-glow 4s ease-in-out infinite;
}

/* Clean section transition */
@keyframes section-fade-in {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.section-fade-in {
  animation: section-fade-in 0.8s ease-out;
}

/* Network Dropdown Scroll Enhancement */
.network-dropdown {
  z-index: 9999;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  backdrop-filter: blur(16px);
  animation: dropdown-slide-down 0.2s ease-out;
}

@keyframes dropdown-slide-down {
  from {
    opacity: 0;
    transform: translateY(-10px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Smooth scroll behavior for the entire page */
html {
  scroll-behavior: smooth;
}

/* Enhanced dropdown visibility during scroll */
.network-dropdown:focus-within {
  z-index: 10000;
}

/* Magnetic hover effect for CTA buttons */
.magnetic-hover {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.magnetic-hover:hover {
  transform: translateY(-2px) scale(1.02);
}

/* Network Selector Container Scroll Target */
#network-selector-container {
  scroll-margin-top: 130px; /* Account for fixed banner height + padding */
  transition: all 0.3s ease-in-out;
}

/* Enhanced scroll behavior for network selection */
.network-selection-scroll {
  scroll-behavior: smooth;
  scroll-padding-top: 130px;
}

/* Smooth transition for network selector positioning */
#network-analysis {
  transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Visual feedback during network selection scroll */
.network-selector-scrolling {
  transform: scale(1.02);
  box-shadow: 0 8px 32px rgba(139, 92, 246, 0.15);
}

/* Network Analysis Section Grouping */
#network-analysis {
  position: relative;
}

/* Statistical Dashboard positioning near Network Selector */
.statistical-dashboard-compact {
  margin-top: 1.5rem;
  margin-bottom: 1.5rem;
}

@media (min-width: 640px) {
  .statistical-dashboard-compact {
    margin-top: 2rem;
    margin-bottom: 2rem;
  }
}

/* Improved spacing for network analysis section */
#network-analysis {
  margin-bottom: 1rem;
}

@media (min-width: 640px) {
  #network-analysis {
    margin-bottom: 1.5rem;
  }
}

/* Tighter spacing between Network Selector and Statistical Dashboard */
.statistical-dashboard-compact {
  margin-top: 1rem;
  margin-bottom: 1.5rem;
}

@media (min-width: 640px) {
  .statistical-dashboard-compact {
    margin-top: 1.5rem;
    margin-bottom: 2rem;
  }
}