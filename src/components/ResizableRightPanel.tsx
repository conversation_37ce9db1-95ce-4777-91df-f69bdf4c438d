import React, { useState, useEffect } from 'react';
import { FaChevronRight, FaChevronLeft } from 'react-icons/fa';
import RightSidePanel from './RightSidePanel';
import ResizeHandle from './ResizeHandle';
import ResizeIndicator from './ResizeIndicator';
import { useLayout } from './LayoutManager';
import useResizable from '@/hooks/useResizable';

interface ResizableRightPanelProps {
  walletData: any;
  selectedWalletPair: any;
  selectedWalletForAnalysis: any;
  showWalletAnalysis: boolean;
  onCloseAnalysis: () => void;
  onClose: () => void;
  onAddToWatchList: (address: string) => void;
  sidebarCollapsed: boolean;
  setSidebarCollapsed: (collapsed: boolean) => void;
  isFullscreen: boolean;
}

const ResizableRightPanel: React.FC<ResizableRightPanelProps> = ({
  walletData,
  selectedWalletPair,
  selectedWalletForAnalysis,
  showWalletAnalysis,
  onCloseAnalysis,
  onClose,
  onAddToWatchList,
  sidebarCollapsed,
  setSidebarCollapsed,
  isFullscreen
}) => {
  // Layout context for dynamic panel widths
  const { layout, updatePanelWidth, getPanelWidth, getDefaultPanelWidth } = useLayout();

  // Resizable hook for wallet analysis panel
  const [resizableState, resizableHandlers] = useResizable({
    minWidth: 320,
    maxWidth: Math.min(800, typeof window !== 'undefined' ? window.innerWidth * 0.6 : 800),
    defaultWidth: getPanelWidth('walletAnalysis'),
    onResize: (width) => {
      updatePanelWidth('walletAnalysis', width);
    }
  });

  // Sync resizable width with layout context on mount and layout changes
  useEffect(() => {
    const currentWidth = getPanelWidth('walletAnalysis');
    if (currentWidth !== resizableState.width) {
      resizableHandlers.setWidth(currentWidth);
    }
  }, [layout.screenSize, layout.isDesktop, layout.isTablet]);

  // Add/remove body class during resize
  useEffect(() => {
    if (resizableState.isResizing) {
      document.body.classList.add('resizing');
    } else {
      document.body.classList.remove('resizing');
    }

    // Cleanup on unmount
    return () => {
      document.body.classList.remove('resizing');
    };
  }, [resizableState.isResizing]);

  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed);
  };

  if (isFullscreen) {
    return null;
  }

  return (
    <>
      {/* Floating Sidebar - Right Edge with proper header spacing and resizable */}
      <div 
        className={`
          resizable-panel absolute top-20 h-[calc(100vh-5rem)] z-30
          ${sidebarCollapsed
            ? 'opacity-0 invisible -right-[32rem]'
            : 'opacity-100 visible right-0'
          }
          ${resizableState.isResizing ? 'resizing' : 'transition-all duration-500 ease-in-out'}
        `} 
        style={{ 
          width: sidebarCollapsed ? 0 : `${resizableState.width}px`,
          maxWidth: 'calc(100vw - 16px)' 
        }}
      >
        {/* Enhanced toggle button */}
        <button
          className="absolute z-30 flex items-center justify-center p-3 transition-all duration-300 transform -translate-y-1/2 border rounded-full -left-4 top-1/2 glass-card hover:bg-accent-500 hover:text-white shadow-bubble border-border-accent group"
          onClick={toggleSidebar}
          aria-label={sidebarCollapsed ? "Expand sidebar" : "Collapse sidebar"}
        >
          <div className="relative">
            {sidebarCollapsed ? (
              <FaChevronLeft size={14} className="transition-transform group-hover:scale-110" />
            ) : (
              <FaChevronRight size={14} className="transition-transform group-hover:scale-110" />
            )}
          </div>
        </button>

        {/* Mobile backdrop */}
        {!sidebarCollapsed && (
          <div
            className="absolute inset-0 lg:hidden bg-black/50 backdrop-blur-sm -z-10"
            onClick={toggleSidebar}
          />
        )}

        <div className="relative h-full border-l bg-background/95 backdrop-blur-lg border-border-secondary">
          {/* Resize Handle */}
          <ResizeHandle
            onMouseDown={resizableHandlers.handleMouseDown}
            onTouchStart={resizableHandlers.handleTouchStart}
            onReset={resizableHandlers.resetWidth}
            isResizing={resizableState.isResizing}
            isDragging={resizableState.isDragging}
            position="left"
            showResetButton={true}
            className="hover:opacity-100"
          />

          <RightSidePanel
            walletData={walletData}
            selectedWalletPair={selectedWalletPair}
            selectedWalletForAnalysis={selectedWalletForAnalysis}
            showWalletAnalysis={showWalletAnalysis}
            onCloseAnalysis={onCloseAnalysis}
            onClose={onClose}
            onAddToWatchList={onAddToWatchList}
          />
        </div>
      </div>

      {/* Resize Indicator */}
      <ResizeIndicator
        width={resizableState.width}
        isVisible={resizableState.isResizing}
        position="center"
      />
    </>
  );
};

export default ResizableRightPanel;
