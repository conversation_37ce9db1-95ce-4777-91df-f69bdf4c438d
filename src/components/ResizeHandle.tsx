import React from 'react';
import { FaGripVertical, FaExpand, FaCompress } from 'react-icons/fa';

export interface ResizeHandleProps {
  onMouseDown: (e: React.MouseEvent) => void;
  onTouchStart: (e: React.TouchEvent) => void;
  onReset?: () => void;
  isResizing: boolean;
  isDragging: boolean;
  position?: 'left' | 'right';
  showResetButton?: boolean;
  className?: string;
}

const ResizeHandle: React.FC<ResizeHandleProps> = ({
  onMouseDown,
  onTouchStart,
  onReset,
  isResizing,
  isDragging,
  position = 'left',
  showResetButton = true,
  className = ''
}) => {
  const handlePosition = position === 'left' ? '-left-2' : '-right-2';
  const hoverTransform = position === 'left' ? 'hover:-translate-x-1' : 'hover:translate-x-1';

  return (
    <div
      className={`
        absolute top-0 bottom-0 z-50 flex flex-col items-center justify-center
        w-4 ${handlePosition} ${hoverTransform}
        transition-all duration-200 ease-in-out
        ${isResizing ? 'opacity-100' : 'opacity-0 hover:opacity-100'}
        ${className}
      `}
    >
      {/* Main resize handle */}
      <div
        className={`
          flex flex-col items-center justify-center
          w-4 h-16 rounded-lg cursor-col-resize
          bg-gradient-to-b from-accent-500/80 to-accent-600/80
          border border-accent-400/30 shadow-lg backdrop-blur-sm
          hover:from-accent-400/90 hover:to-accent-500/90
          hover:border-accent-300/50 hover:shadow-xl
          active:from-accent-600/90 active:to-accent-700/90
          transition-all duration-200 ease-in-out
          ${isDragging ? 'scale-110 shadow-2xl' : 'hover:scale-105'}
          ${isResizing ? 'animate-pulse' : ''}
        `}
        onMouseDown={onMouseDown}
        onTouchStart={onTouchStart}
        title="Drag to resize panel"
      >
        <FaGripVertical 
          className={`
            text-white/90 transition-all duration-200
            ${isDragging ? 'text-lg' : 'text-sm hover:text-base'}
          `} 
        />
      </div>

      {/* Reset button */}
      {showResetButton && onReset && (
        <button
          onClick={onReset}
          className={`
            mt-2 p-2 rounded-md cursor-pointer
            bg-background-secondary/80 border border-border-accent/50
            hover:bg-background-tertiary hover:border-border-accent
            active:bg-background-quaternary
            transition-all duration-200 ease-in-out
            hover:scale-105 active:scale-95
            ${isResizing ? 'opacity-100' : 'opacity-0 hover:opacity-100'}
          `}
          title="Reset to default width"
        >
          <FaCompress className="text-xs text-foreground-muted hover:text-foreground" />
        </button>
      )}

      {/* Visual indicator dots */}
      <div className={`
        absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2
        flex flex-col gap-1 pointer-events-none
        ${isDragging ? 'opacity-100' : 'opacity-0'}
        transition-opacity duration-200
      `}>
        {[...Array(3)].map((_, i) => (
          <div
            key={i}
            className={`
              w-1 h-1 rounded-full bg-white/60
              ${isDragging ? 'animate-pulse' : ''}
            `}
            style={{
              animationDelay: `${i * 0.1}s`
            }}
          />
        ))}
      </div>

      {/* Resize cursor area extension */}
      <div
        className={`
          absolute inset-y-0 cursor-col-resize
          ${position === 'left' ? '-left-2 -right-1' : '-right-2 -left-1'}
          ${isResizing ? 'bg-accent-500/10' : ''}
        `}
        onMouseDown={onMouseDown}
        onTouchStart={onTouchStart}
      />
    </div>
  );
};

export default ResizeHandle;
