import React, { useState, useEffect, useCallback, useRef } from 'react';
import { FaChevronRight, FaChevronLeft, FaGripVertical, FaCompress } from 'react-icons/fa';
import RightSidePanel from './RightSidePanel';

interface SimpleResizablePanelProps {
  walletData: any;
  selectedWalletPair: any;
  selectedWalletForAnalysis: any;
  showWalletAnalysis: boolean;
  onCloseAnalysis: () => void;
  onClose: () => void;
  onAddToWatchList: (address: string) => void;
  sidebarCollapsed: boolean;
  setSidebarCollapsed: (collapsed: boolean) => void;
  isFullscreen: boolean;
}

const SimpleResizablePanel: React.FC<SimpleResizablePanelProps> = ({
  walletData,
  selectedWalletPair,
  selectedWalletForAnalysis,
  showWalletAnalysis,
  onCloseAnalysis,
  onClose,
  onAddToWatchList,
  sidebarCollapsed,
  setSidebarCollapsed,
  isFullscreen
}) => {
  // Panel width state
  const [panelWidth, setPanelWidth] = useState(448); // Default 28rem
  const [isResizing, setIsResizing] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  
  // Refs for resize handling
  const startXRef = useRef<number>(0);
  const startWidthRef = useRef<number>(448);

  // Constants
  const MIN_WIDTH = 320;
  const MAX_WIDTH = Math.min(800, typeof window !== 'undefined' ? window.innerWidth * 0.6 : 800);

  // Constrain width within bounds
  const constrainWidth = useCallback((width: number): number => {
    return Math.max(MIN_WIDTH, Math.min(MAX_WIDTH, width));
  }, []);

  // Handle mouse/touch move
  const handleMove = useCallback((clientX: number) => {
    if (!isResizing) return;

    const deltaX = clientX - startXRef.current;
    const newWidth = startWidthRef.current + deltaX;
    const constrainedWidth = constrainWidth(newWidth);
    setPanelWidth(constrainedWidth);
  }, [isResizing, constrainWidth]);

  // Handle mouse/touch end
  const handleEnd = useCallback(() => {
    setIsResizing(false);
    setIsDragging(false);
    document.body.style.cursor = '';
    document.body.style.userSelect = '';
    document.body.classList.remove('resizing');
  }, []);

  // Mouse event handlers
  const handleMouseMove = useCallback((e: MouseEvent) => {
    e.preventDefault();
    handleMove(e.clientX);
  }, [handleMove]);

  const handleMouseUp = useCallback((e: MouseEvent) => {
    e.preventDefault();
    handleEnd();
  }, [handleEnd]);

  // Touch event handlers
  const handleTouchMove = useCallback((e: TouchEvent) => {
    e.preventDefault();
    if (e.touches.length > 0) {
      handleMove(e.touches[0].clientX);
    }
  }, [handleMove]);

  const handleTouchEnd = useCallback((e: TouchEvent) => {
    e.preventDefault();
    handleEnd();
  }, [handleEnd]);

  // Start resize handlers
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    startXRef.current = e.clientX;
    startWidthRef.current = panelWidth;

    setIsResizing(true);
    setIsDragging(true);

    // Add global event listeners
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
    
    // Set cursor style
    document.body.style.cursor = 'col-resize';
    document.body.style.userSelect = 'none';
    document.body.classList.add('resizing');
  }, [panelWidth, handleMouseMove, handleMouseUp]);

  const handleTouchStart = useCallback((e: React.TouchEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (e.touches.length > 0) {
      startXRef.current = e.touches[0].clientX;
      startWidthRef.current = panelWidth;

      setIsResizing(true);
      setIsDragging(true);

      // Add global event listeners
      document.addEventListener('touchmove', handleTouchMove, { passive: false });
      document.addEventListener('touchend', handleTouchEnd);
    }
  }, [panelWidth, handleTouchMove, handleTouchEnd]);

  // Reset to default width
  const resetWidth = useCallback(() => {
    setPanelWidth(448);
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
      document.removeEventListener('touchmove', handleTouchMove);
      document.removeEventListener('touchend', handleTouchEnd);
      handleEnd();
    };
  }, [handleMouseMove, handleMouseUp, handleTouchMove, handleTouchEnd, handleEnd]);

  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed);
  };

  if (isFullscreen) {
    return null;
  }

  return (
    <>
      {/* Floating Sidebar - Right Edge with proper header spacing and resizable */}
      <div 
        className={`
          absolute top-20 h-[calc(100vh-5rem)] z-30
          ${sidebarCollapsed
            ? 'opacity-0 invisible -right-[32rem]'
            : 'opacity-100 visible right-0'
          }
          ${isResizing ? '' : 'transition-all duration-500 ease-in-out'}
        `} 
        style={{ 
          width: sidebarCollapsed ? 0 : `${panelWidth}px`,
          maxWidth: 'calc(100vw - 16px)' 
        }}
      >
        {/* Enhanced toggle button */}
        <button
          className="absolute z-30 flex items-center justify-center p-3 transition-all duration-300 transform -translate-y-1/2 border rounded-full -left-4 top-1/2 glass-card hover:bg-accent-500 hover:text-white shadow-bubble border-border-accent group"
          onClick={toggleSidebar}
          aria-label={sidebarCollapsed ? "Expand sidebar" : "Collapse sidebar"}
        >
          <div className="relative">
            {sidebarCollapsed ? (
              <FaChevronLeft size={14} className="transition-transform group-hover:scale-110" />
            ) : (
              <FaChevronRight size={14} className="transition-transform group-hover:scale-110" />
            )}
          </div>
        </button>

        {/* Mobile backdrop */}
        {!sidebarCollapsed && (
          <div
            className="absolute inset-0 lg:hidden bg-black/50 backdrop-blur-sm -z-10"
            onClick={toggleSidebar}
          />
        )}

        <div className="relative h-full border-l bg-background/95 backdrop-blur-lg border-border-secondary">
          {/* Resize Handle */}
          <div
            className={`
              absolute top-0 bottom-0 z-50 flex flex-col items-center justify-center
              w-4 -left-2 hover:-translate-x-1
              transition-all duration-200 ease-in-out
              ${isResizing ? 'opacity-100' : 'opacity-0 hover:opacity-100'}
            `}
          >
            {/* Main resize handle */}
            <div
              className={`
                flex flex-col items-center justify-center
                w-4 h-16 rounded-lg cursor-col-resize
                bg-gradient-to-b from-accent-500/80 to-accent-600/80
                border border-accent-400/30 shadow-lg backdrop-blur-sm
                hover:from-accent-400/90 hover:to-accent-500/90
                hover:border-accent-300/50 hover:shadow-xl
                active:from-accent-600/90 active:to-accent-700/90
                transition-all duration-200 ease-in-out
                ${isDragging ? 'scale-110 shadow-2xl' : 'hover:scale-105'}
                ${isResizing ? 'animate-pulse' : ''}
              `}
              onMouseDown={handleMouseDown}
              onTouchStart={handleTouchStart}
              title="Drag to resize panel"
            >
              <FaGripVertical 
                className={`
                  text-white/90 transition-all duration-200
                  ${isDragging ? 'text-lg' : 'text-sm hover:text-base'}
                `} 
              />
            </div>

            {/* Reset button */}
            <button
              onClick={resetWidth}
              className={`
                mt-2 p-2 rounded-md cursor-pointer
                bg-background-secondary/80 border border-border-accent/50
                hover:bg-background-tertiary hover:border-border-accent
                active:bg-background-quaternary
                transition-all duration-200 ease-in-out
                hover:scale-105 active:scale-95
                ${isResizing ? 'opacity-100' : 'opacity-0 hover:opacity-100'}
              `}
              title="Reset to default width"
            >
              <FaCompress className="text-xs text-foreground-muted hover:text-foreground" />
            </button>
          </div>

          <RightSidePanel
            walletData={walletData}
            selectedWalletPair={selectedWalletPair}
            selectedWalletForAnalysis={selectedWalletForAnalysis}
            showWalletAnalysis={showWalletAnalysis}
            onCloseAnalysis={onCloseAnalysis}
            onClose={onClose}
            onAddToWatchList={onAddToWatchList}
          />
        </div>
      </div>

      {/* Resize Indicator */}
      {isResizing && (
        <div className="fixed z-[9999] pointer-events-none top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
          <div className="flex items-center gap-2 px-3 py-2 rounded-lg bg-background-secondary/95 border border-border-accent backdrop-blur-md shadow-lg animate-in fade-in-0 zoom-in-95 duration-200">
            <FaGripVertical className="text-accent-400 text-sm" />
            <span className="text-sm font-mono font-medium text-foreground">
              {panelWidth}px
            </span>
            <div className="w-2 h-2 rounded-full bg-accent-400 animate-pulse" />
          </div>
          
          <div className="mt-1 text-center text-xs text-foreground-muted opacity-70">
            {typeof window !== 'undefined' && (
              <span>
                {((panelWidth / window.innerWidth) * 100).toFixed(1)}% of screen
              </span>
            )}
          </div>
        </div>
      )}
    </>
  );
};

export default SimpleResizablePanel;
