import React from 'react';
import { FaRulerHorizontal } from 'react-icons/fa';

export interface ResizeIndicatorProps {
  width: number;
  isVisible: boolean;
  position?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right' | 'center';
  className?: string;
}

const ResizeIndicator: React.FC<ResizeIndicatorProps> = ({
  width,
  isVisible,
  position = 'top-right',
  className = ''
}) => {
  const getPositionClasses = () => {
    switch (position) {
      case 'top-left':
        return 'top-4 left-4';
      case 'top-right':
        return 'top-4 right-4';
      case 'bottom-left':
        return 'bottom-4 left-4';
      case 'bottom-right':
        return 'bottom-4 right-4';
      case 'center':
        return 'top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2';
      default:
        return 'top-4 right-4';
    }
  };

  if (!isVisible) return null;

  return (
    <div
      className={`
        fixed z-[9999] pointer-events-none
        ${getPositionClasses()}
        ${className}
      `}
    >
      <div className={`
        flex items-center gap-2 px-3 py-2 rounded-lg
        bg-background-secondary/95 border border-border-accent
        backdrop-blur-md shadow-lg
        animate-in fade-in-0 zoom-in-95 duration-200
        ${isVisible ? 'opacity-100' : 'opacity-0'}
        transition-opacity duration-200
      `}>
        <FaRulerHorizontal className="text-accent-400 text-sm" />
        <span className="text-sm font-mono font-medium text-foreground">
          {width}px
        </span>
        <div className="w-2 h-2 rounded-full bg-accent-400 animate-pulse" />
      </div>
      
      {/* Optional: Show percentage of screen width */}
      <div className={`
        mt-1 text-center text-xs text-foreground-muted
        ${isVisible ? 'opacity-70' : 'opacity-0'}
        transition-opacity duration-200 delay-100
      `}>
        {typeof window !== 'undefined' && (
          <span>
            {((width / window.innerWidth) * 100).toFixed(1)}% of screen
          </span>
        )}
      </div>
    </div>
  );
};

export default ResizeIndicator;
