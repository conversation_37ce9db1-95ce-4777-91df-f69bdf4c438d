import React, { createContext, useContext, useState, useEffect } from 'react';

interface LayoutState {
  showWalletAnalysis: boolean;
  showTransactionFlow: boolean;
  showSecurityAlerts: boolean;
  showRiskFilter: boolean;
  showWatchList: boolean;
  showAskAI: boolean;
  isFullscreen: boolean;
  sidebarCollapsed: boolean;
  screenSize: 'sm' | 'md' | 'lg' | 'xl' | '2xl';
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
}

interface ComponentPosition {
  position: string;
  zIndex: number;
  width: string;
  height?: string;
  transform?: string;
  maxWidth?: string;
  maxHeight?: string;
  overflow?: string;
}

interface LayoutContextType {
  layout: LayoutState;
  updateLayout: (updates: Partial<LayoutState>) => void;
  getComponentPosition: (component: string) => ComponentPosition;
  isComponentVisible: (component: string) => boolean;
  getAvailableSpace: () => {
    left: number;
    right: number;
    top: number;
    bottom: number;
  };
  getViewportInfo: () => {
    width: number;
    height: number;
    headerHeight: number;
    footerHeight: number;
  };
}

const LayoutContext = createContext<LayoutContextType | undefined>(undefined);

export const useLayout = () => {
  const context = useContext(LayoutContext);
  if (!context) {
    throw new Error('useLayout must be used within a LayoutProvider');
  }
  return context;
};

interface LayoutProviderProps {
  children: React.ReactNode;
}

export const LayoutProvider: React.FC<LayoutProviderProps> = ({ children }) => {
  const [layout, setLayout] = useState<LayoutState>({
    showWalletAnalysis: false,
    showTransactionFlow: false,
    showSecurityAlerts: true,
    showRiskFilter: false,
    showWatchList: false,
    showAskAI: false,
    isFullscreen: false,
    sidebarCollapsed: false,
    screenSize: 'lg',
    isMobile: false,
    isTablet: false,
    isDesktop: true
  });

  const [isHydrated, setIsHydrated] = useState(false);

  // Enhanced screen size detection with device type classification and hydration
  useEffect(() => {
    const updateScreenSize = () => {
      if (typeof window === 'undefined') return;

      const width = window.innerWidth;

      // Determine screen size category
      let screenSize: 'sm' | 'md' | 'lg' | 'xl' | '2xl';
      if (width < 640) screenSize = 'sm';
      else if (width < 768) screenSize = 'md';
      else if (width < 1024) screenSize = 'lg';
      else if (width < 1280) screenSize = 'xl';
      else screenSize = '2xl';

      // Determine device type for layout optimization
      const isMobile = width < 768;
      const isTablet = width >= 768 && width < 1024;
      const isDesktop = width >= 1024;

      setLayout(prev => ({
        ...prev,
        screenSize,
        isMobile,
        isTablet,
        isDesktop
      }));
    };

    // Load persisted state and initialize after hydration
    if (typeof window !== 'undefined') {
      const savedTransactionFlow = localStorage.getItem('bubbleMap_showTransactionFlow');
      const savedSecurityAlerts = localStorage.getItem('bubbleMap_showSecurityAlerts');
      const savedRiskFilter = localStorage.getItem('bubbleMap_showRiskFilter');

      setLayout(prev => ({
        ...prev,
        showTransactionFlow: savedTransactionFlow ? JSON.parse(savedTransactionFlow) : prev.showTransactionFlow,
        showSecurityAlerts: savedSecurityAlerts ? JSON.parse(savedSecurityAlerts) : prev.showSecurityAlerts,
        showRiskFilter: savedRiskFilter ? JSON.parse(savedRiskFilter) : prev.showRiskFilter
      }));

      setIsHydrated(true);
      updateScreenSize();

      window.addEventListener('resize', updateScreenSize);
      return () => window.removeEventListener('resize', updateScreenSize);
    }
  }, []);

  const updateLayout = (updates: Partial<LayoutState>) => {
    setLayout(prev => ({ ...prev, ...updates }));
  };

  const getComponentPosition = (component: string): ComponentPosition => {
    const { isMobile, isTablet } = layout;

    switch (component) {
      case 'controlPanel':
        if (isMobile) {
          // Mobile: Bottom floating action button pattern
          return {
            position: 'fixed bottom-6 left-1/2 transform -translate-x-1/2',
            zIndex: 45,
            width: 'auto',
            transform: '-translate-x-1/2'
          };
        } else if (isTablet) {
          // Tablet: Top-left compact layout
          return {
            position: 'fixed top-20 left-4',
            zIndex: 45,
            width: 'auto',
            transform: 'none'
          };
        } else {
          // Desktop: Left side, below header
          return {
            position: 'fixed top-20 left-6',
            zIndex: 45,
            width: 'auto',
            transform: 'none'
          };
        }

      case 'walletAnalysis':
        if (isMobile) {
          // Mobile: Full-screen modal overlay
          return {
            position: 'fixed inset-4 top-20',
            zIndex: 35,
            width: 'calc(100vw - 2rem)',
            maxWidth: '100vw',
            maxHeight: 'calc(100vh - 6rem)',
            overflow: 'hidden'
          };
        } else if (isTablet) {
          // Tablet: Right side overlay
          return {
            position: 'fixed right-4 top-20 bottom-4',
            zIndex: 35,
            width: 'calc(50vw - 2rem)',
            maxWidth: '28rem',
            overflow: 'hidden'
          };
        } else {
          // Desktop: Right side panel
          return {
            position: 'fixed right-4 top-20 bottom-4',
            zIndex: 35,
            width: 'w-96',
            maxWidth: '24rem',
            overflow: 'hidden'
          };
        }

      case 'leftSidebar':
        if (isMobile) {
          // Mobile: Bottom sheet/drawer pattern
          return {
            position: 'fixed bottom-0 left-0 right-0',
            zIndex: 40,
            width: '100vw',
            maxHeight: '70vh',
            overflow: 'hidden'
          };
        } else if (isTablet) {
          // Tablet: Left side with proper spacing, closer to control panel
          return {
            position: 'fixed left-4 top-32',
            zIndex: 40,
            width: 'calc(50vw - 2rem)',
            maxWidth: '20rem',
            maxHeight: 'calc(100vh - 10rem)', // Reduced height for better proportions
            overflow: 'hidden'
          };
        } else {
          // Desktop: Left side, closer to control panel with optimized height
          return {
            position: 'fixed left-6 top-32',
            zIndex: 40,
            width: '24rem',
            maxHeight: 'calc(100vh - 8rem)', // Reduced height: 100vh - 8rem (header + margins)
            overflow: 'hidden'
          };
        }

      case 'tooltip':
        // Enhanced tooltip positioning with center-right viewport strategy
        return {
          position: 'fixed',
          zIndex: 9999, // Highest priority - always visible
          width: 'auto',
          maxWidth: isMobile ? '85vw' : '20rem',
          transform: 'translate(-50%, -50%)'
        };

      case 'watchList':
        if (isMobile) {
          // Mobile: Full-screen modal overlay
          return {
            position: 'fixed inset-0',
            zIndex: 50,
            width: '100vw',
            height: '100vh',
            overflow: 'hidden'
          };
        } else if (isTablet) {
          // Tablet: Right side panel
          return {
            position: 'fixed right-4 top-20',
            zIndex: 40,
            width: 'calc(50vw - 2rem)',
            maxWidth: '24rem',
            maxHeight: 'calc(100vh - 6rem)',
            overflow: 'hidden'
          };
        } else {
          // Desktop: Right side panel
          return {
            position: 'fixed right-6 top-20',
            zIndex: 40,
            width: '28rem',
            maxHeight: 'calc(100vh - 6rem)',
            overflow: 'hidden'
          };
        }

      case 'debugPanel':
        return {
          position: 'fixed top-4 right-4',
          zIndex: 60,
          width: 'auto'
        };

      case 'loadingOverlay':
        return {
          position: 'fixed inset-0',
          zIndex: 8000, // Below tooltips but above all other content
          width: '100%',
          height: '100%'
        };

      default:
        return {
          position: 'relative',
          zIndex: 1,
          width: 'auto'
        };
    }
  };

  const isComponentVisible = (component: string) => {
    switch (component) {
      case 'walletAnalysis':
        return layout.showWalletAnalysis;
      case 'transactionFlow':
        return layout.showTransactionFlow;
      case 'securityAlerts':
        return layout.showSecurityAlerts;
      case 'riskFilter':
        return layout.showRiskFilter;
      case 'watchList':
        return layout.showWatchList;
      case 'askAI':
        return layout.showAskAI;
      default:
        return true;
    }
  };

  const getAvailableSpace = () => {
    const { isMobile, isTablet, showWalletAnalysis, showTransactionFlow, showSecurityAlerts, showWatchList } = layout;

    // Enhanced spacing calculation based on device type
    const spacing = isMobile ? 16 : isTablet ? 24 : 32;
    const headerHeight = 80;
    const controlPanelSpace = isMobile ? 80 : 160; // Reduced desktop control panel space

    let left = spacing;
    let right = spacing;
    let top = headerHeight + spacing;
    let bottom = spacing;

    // Adjust for visible panels based on device type
    if (isMobile) {
      // Mobile: panels are overlays, don't reduce available space
      bottom += 80; // Account for bottom control panel
    } else if (isTablet) {
      // Tablet: side panels reduce available space
      if (showTransactionFlow || showSecurityAlerts || showWatchList) {
        left += 320; // Sidebar width + margin
      }
      if (showWalletAnalysis) {
        right += 320; // Analysis panel width + margin
      }
      if (showWatchList) {
        right += 320; // Watch list panel width + margin
      }
    } else {
      // Desktop: all panels reduce available space with optimized spacing
      left += controlPanelSpace; // Reduced control panel space
      if (showTransactionFlow || showSecurityAlerts || showWatchList) {
        left += 384; // Sidebar width
      }
      if (showWalletAnalysis) {
        right += 384; // Analysis panel width
      }
      if (showWatchList) {
        right += 448; // Watch list panel width (28rem = 448px)
      }
    }

    return { left, right, top, bottom };
  };

  const getViewportInfo = () => {
    if (typeof window === 'undefined') {
      return { width: 1024, height: 768, headerHeight: 80, footerHeight: 0 };
    }

    return {
      width: window.innerWidth,
      height: window.innerHeight,
      headerHeight: 80,
      footerHeight: layout.isMobile ? 80 : 0 // Mobile has bottom control panel
    };
  };

  const contextValue: LayoutContextType = {
    layout,
    updateLayout,
    getComponentPosition,
    isComponentVisible,
    getAvailableSpace,
    getViewportInfo
  };

  return (
    <LayoutContext.Provider value={contextValue}>
      {children}
    </LayoutContext.Provider>
  );
};

// Utility component for responsive positioning
interface ResponsiveContainerProps {
  component: string;
  children: React.ReactNode;
  className?: string;
}

export const ResponsiveContainer: React.FC<ResponsiveContainerProps> = ({
  component,
  children,
  className = ''
}) => {
  const { getComponentPosition, isComponentVisible, layout } = useLayout();

  if (!isComponentVisible(component)) {
    return null;
  }

  const position = getComponentPosition(component);

  // Build CSS classes from position object
  const positionClasses = [
    position.position,
    position.width,
    position.height
  ].filter(Boolean).join(' ');

  // Enhanced style object with new properties
  const style: React.CSSProperties = {
    zIndex: position.zIndex,
    transform: position.transform || undefined,
    maxWidth: position.maxWidth || undefined,
    maxHeight: position.maxHeight || undefined,
    overflow: position.overflow || undefined
  };

  // Add responsive classes based on device type
  const responsiveClasses = [
    layout.isMobile && 'mobile-layout',
    layout.isTablet && 'tablet-layout',
    layout.isDesktop && 'desktop-layout'
  ].filter(Boolean).join(' ');

  return (
    <div
      className={`transition-all duration-300 ease-in-out ${positionClasses} ${responsiveClasses} ${className}`}
      style={style}
      data-component={component}
      data-device-type={layout.isMobile ? 'mobile' : layout.isTablet ? 'tablet' : 'desktop'}
    >
      {children}
    </div>
  );
};
