import { useState, useEffect } from 'react';
import Head from 'next/head';
import BubbleMapContainer from '@/components/BubbleMapContainer';
import SearchBar from '@/components/SearchBar';
import Header from '@/components/Header';
import SimpleResizablePanel from '@/components/SimpleResizablePanel';
import { FaChevronRight, FaChevronLeft, FaExpand, FaCompress } from 'react-icons/fa';

export default function BubbleMap() {
  const [searchWallet, setSearchWallet] = useState<string>('');
  const [selectedWallet, setSelectedWallet] = useState<any>(null);
  const [selectedWalletForAnalysis, setSelectedWalletForAnalysis] = useState<any>(null);
  const [selectedWalletPair, setSelectedWalletPair] = useState<{sourceWallet: any, targetWallet: any} | null>(null);
  const [showWalletAnalysis, setShowWalletAnalysis] = useState<boolean>(false);
  const [sidebarCollapsed, setSidebarCollapsed] = useState<boolean>(false);
  const [isFullscreen, setIsFullscreen] = useState<boolean>(false);
  const [viewportHeight, setViewportHeight] = useState<number>(0);
  const [globalError, setGlobalError] = useState<string | null>(null);

  // Dynamic viewport height calculation
  useEffect(() => {
    const updateViewportHeight = () => {
      setViewportHeight(window.innerHeight);
    };

    updateViewportHeight();
    window.addEventListener('resize', updateViewportHeight);
    return () => window.removeEventListener('resize', updateViewportHeight);
  }, []);

  const handleSearch = (address: string) => {
    setSearchWallet(address);
  };

  const handleSelectWallet = (wallet: any) => {
    console.log("Wallet selected in BubbleMap component:", wallet);
    // Make sure we have a complete wallet object with all required properties
    const completeWallet = {
      id: wallet.id || '',
      address: wallet.address || '',
      label: wallet.label,
      balance: wallet.balance,
      transactionCount: wallet.transactionCount,
      tags: wallet.tags || [],
      // Social media connectivity
      socialProfiles: wallet.socialProfiles,
      hasVerifiedSocials: wallet.hasVerifiedSocials,
      socialScore: wallet.socialScore,
      // Add any other properties needed for display
    };

    setSelectedWallet(completeWallet);
    setSelectedWalletForAnalysis(wallet); // Set for analysis panel
    setShowWalletAnalysis(true); // Show analysis panel
    // Clear wallet pair when selecting individual wallet
    setSelectedWalletPair(null);
    setSidebarCollapsed(false); // Open sidebar when a wallet is selected
  };

  const handleSelectWalletPair = (pair: {sourceWallet: any, targetWallet: any}) => {
    console.log("Wallet pair selected:", pair);
    setSelectedWalletPair(pair);
    // Also set the source wallet as the primary selected wallet
    setSelectedWallet(pair.sourceWallet);
    setSelectedWalletForAnalysis(pair.sourceWallet);
    setShowWalletAnalysis(true);
    setSidebarCollapsed(false);
  };

  // Remove the toggleSidebar function as it's now handled in ResizableRightPanel

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
    if (!isFullscreen) {
      setSidebarCollapsed(true);
    }
  };

  // Handle adding wallet to watch list
  const handleAddToWatchList = (address: string) => {
    console.log('Wallet added to watch list:', address);
    // You can add additional logic here like showing a success message
  };

  // Handle focusing on a wallet from watch list
  const handleFocusWallet = (address: string) => {
    console.log('Focusing on wallet:', address);
    // Set the search wallet to trigger a search and focus
    setSearchWallet(address);
  };

  // Calculate dynamic heights
  const headerHeight = 80; // Approximate header height
  const searchHeight = 80; // Search bar height with padding
  const availableHeight = viewportHeight - headerHeight - (isFullscreen ? 0 : searchHeight);

  return (
    <>
      <Head>
        <title>Bubble Map - Crypto Bubble Map</title>
        <meta name="description" content="Interactive bubble map visualization of cryptocurrency wallet relationships and transaction networks" />
      </Head>

      <main className="h-screen overflow-hidden bg-background" style={{ maxWidth: '100vw', overflowX: 'hidden' }}>
        {/* Integrated Header with Search Bar */}
        <div className="absolute top-0 left-0 right-0 z-50 border-b bg-background/80 backdrop-blur-lg border-border/50">
          <Header onSearch={handleSearch} showSearch={!isFullscreen} />
        </div>

        {/* Global Error Notification - Always visible below header */}
        {globalError && (
          <div className="absolute z-[60] max-w-md mx-4 transform -translate-x-1/2 top-20 left-1/2">
            <div className="px-6 py-4 border glass-card rounded-xl shadow-bubble border-red-500/30 animate-slide-down">
              <div className="flex items-center gap-3">
                <div className="flex-shrink-0 w-2 h-2 bg-red-400 rounded-full animate-pulse"></div>
                <span className="text-sm font-medium leading-relaxed text-red-400">{globalError}</span>
                <button
                  onClick={() => setGlobalError(null)}
                  className="ml-auto text-red-400 transition-colors hover:text-red-300"
                  aria-label="Dismiss error"
                >
                  ×
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Edge-to-Edge Bubble Map Container */}
        <div className="absolute inset-0">
          {/* Fullscreen controls overlay */}
          {isFullscreen && (
            <div className="absolute z-30 flex gap-3 top-6 left-6">
              <div className="p-3 glass-card rounded-xl">
                <SearchBar onSearch={handleSearch} compact={true} />
              </div>
              <button
                onClick={toggleFullscreen}
                className="p-3 transition-all duration-300 border glass-card hover:bg-accent-500 hover:text-white rounded-xl shadow-glow border-border-accent group"
                title="Exit Fullscreen"
              >
                <FaCompress size={16} className="transition-transform group-hover:scale-110" />
              </button>
            </div>
          )}

          {/* Full Viewport Bubble Map - Respects Header Space */}
          <div className={`absolute top-20 left-0 bottom-0 transition-all duration-500 ease-in-out ${
            isFullscreen || sidebarCollapsed
              ? 'right-0'
              : 'right-0' // Always use right-0 to prevent overflow, sidebar will overlay
          }`} style={{ maxWidth: '100vw', overflowX: 'hidden' }}>
            <BubbleMapContainer
              searchWallet={searchWallet}
              onSelectWallet={handleSelectWallet}
              onSelectWalletPair={handleSelectWalletPair}
              onError={setGlobalError}
              onFocusWallet={handleFocusWallet}
            />
          </div>

        {/* Simple Resizable Right Panel */}
        <SimpleResizablePanel
          walletData={selectedWallet}
          selectedWalletPair={selectedWalletPair}
          selectedWalletForAnalysis={selectedWalletForAnalysis}
          showWalletAnalysis={showWalletAnalysis}
          onCloseAnalysis={() => {
            setShowWalletAnalysis(false);
            setSelectedWalletForAnalysis(null);
            setSelectedWalletPair(null);
          }}
          onClose={() => {
            setSidebarCollapsed(true);
            setSelectedWallet(null);
            setSelectedWalletForAnalysis(null);
            setSelectedWalletPair(null);
            setShowWalletAnalysis(false);
          }}
          onAddToWatchList={handleAddToWatchList}
          sidebarCollapsed={sidebarCollapsed}
          setSidebarCollapsed={setSidebarCollapsed}
          isFullscreen={isFullscreen}
        />

        </div>
      </main>
    </>
  );
}
