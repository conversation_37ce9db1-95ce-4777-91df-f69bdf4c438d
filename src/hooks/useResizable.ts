import { useState, useCallback, useRef, useEffect } from 'react';

export interface ResizableConfig {
  minWidth: number;
  maxWidth: number;
  defaultWidth: number;
  step?: number;
  onResize?: (width: number) => void;
}

export interface ResizableState {
  width: number;
  isResizing: boolean;
  isDragging: boolean;
}

export interface ResizableHandlers {
  handleMouseDown: (e: React.MouseEvent) => void;
  handleTouchStart: (e: React.TouchEvent) => void;
  setWidth: (width: number) => void;
  resetWidth: () => void;
}

export const useResizable = (config: ResizableConfig): [ResizableState, ResizableHandlers] => {
  const { minWidth, maxWidth, defaultWidth, step = 1, onResize } = config;
  
  const [state, setState] = useState<ResizableState>({
    width: defaultWidth,
    isResizing: false,
    isDragging: false
  });

  const startXRef = useRef<number>(0);
  const startWidthRef = useRef<number>(defaultWidth);
  const rafRef = useRef<number>();

  // Constrain width within bounds
  const constrainWidth = useCallback((width: number): number => {
    const constrainedWidth = Math.max(minWidth, Math.min(maxWidth, width));
    return step > 1 ? Math.round(constrainedWidth / step) * step : constrainedWidth;
  }, [minWidth, maxWidth, step]);

  // Update width with constraints
  const setWidth = useCallback((newWidth: number) => {
    const constrainedWidth = constrainWidth(newWidth);
    setState(prev => ({ ...prev, width: constrainedWidth }));
    onResize?.(constrainedWidth);
  }, [constrainWidth, onResize]);

  // Reset to default width
  const resetWidth = useCallback(() => {
    setWidth(defaultWidth);
  }, [defaultWidth, setWidth]);

  // Handle mouse/touch move
  const handleMove = useCallback((clientX: number) => {
    if (!state.isResizing) return;

    // Cancel any pending animation frame
    if (rafRef.current) {
      cancelAnimationFrame(rafRef.current);
    }

    // Use requestAnimationFrame for smooth resizing
    rafRef.current = requestAnimationFrame(() => {
      const deltaX = clientX - startXRef.current;
      const newWidth = startWidthRef.current + deltaX;
      setWidth(newWidth);
    });
  }, [state.isResizing, setWidth]);

  // Handle mouse/touch end
  const handleEnd = useCallback(() => {
    setState(prev => ({ 
      ...prev, 
      isResizing: false, 
      isDragging: false 
    }));

    // Cancel any pending animation frame
    if (rafRef.current) {
      cancelAnimationFrame(rafRef.current);
      rafRef.current = undefined;
    }

    // Remove event listeners
    document.removeEventListener('mousemove', handleMouseMove);
    document.removeEventListener('mouseup', handleMouseUp);
    document.removeEventListener('touchmove', handleTouchMove);
    document.removeEventListener('touchend', handleTouchEnd);
    
    // Remove cursor style
    document.body.style.cursor = '';
    document.body.style.userSelect = '';
  }, []);

  // Mouse event handlers
  const handleMouseMove = useCallback((e: MouseEvent) => {
    e.preventDefault();
    handleMove(e.clientX);
  }, [handleMove]);

  const handleMouseUp = useCallback((e: MouseEvent) => {
    e.preventDefault();
    handleEnd();
  }, [handleEnd]);

  // Touch event handlers
  const handleTouchMove = useCallback((e: TouchEvent) => {
    e.preventDefault();
    if (e.touches.length > 0) {
      handleMove(e.touches[0].clientX);
    }
  }, [handleMove]);

  const handleTouchEnd = useCallback((e: TouchEvent) => {
    e.preventDefault();
    handleEnd();
  }, [handleEnd]);

  // Start resize handlers
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    startXRef.current = e.clientX;
    startWidthRef.current = state.width;

    setState(prev => ({ 
      ...prev, 
      isResizing: true, 
      isDragging: true 
    }));

    // Add global event listeners
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
    
    // Set cursor style
    document.body.style.cursor = 'col-resize';
    document.body.style.userSelect = 'none';
  }, [state.width, handleMouseMove, handleMouseUp]);

  const handleTouchStart = useCallback((e: React.TouchEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (e.touches.length > 0) {
      startXRef.current = e.touches[0].clientX;
      startWidthRef.current = state.width;

      setState(prev => ({ 
        ...prev, 
        isResizing: true, 
        isDragging: true 
      }));

      // Add global event listeners
      document.addEventListener('touchmove', handleTouchMove, { passive: false });
      document.addEventListener('touchend', handleTouchEnd);
    }
  }, [state.width, handleTouchMove, handleTouchEnd]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (rafRef.current) {
        cancelAnimationFrame(rafRef.current);
      }
      handleEnd();
    };
  }, [handleEnd]);

  return [
    state,
    {
      handleMouseDown,
      handleTouchStart,
      setWidth,
      resetWidth
    }
  ];
};

export default useResizable;
